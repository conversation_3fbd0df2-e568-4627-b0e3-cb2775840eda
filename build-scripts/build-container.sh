#!/bin/bash

# Check if the LSP Account Name environment variable exists and has a value
if [ -z "${LSP_ACCOUNT_NAME}" ]; then
    echo "Environment variable LSP_ACCOUNT_NAME is not set or is empty."
    exit -1
fi

# Initialize version components
major=1
minor=5
patch=0

# Check if version file exists
# if [ -f buildVersion.txt ]; then
#     # Read version from file
#     read major minor patch < buildVersion.txt
# fi

# Increment the minor version
((minor++))

# Update version file with new version
echo "$major $minor $patch" > buildVersion.txt

# Create a version string
LSPVersion="$major.$minor.$patch";

# Create a docker version tag
versionTag=("lsp:"$LSPVersion)

current_date_time=$(date +"%Y-%m-%d %H:%M:%S")

echo "LSP Version: $LSPVersion built on $current_date_time" > LSPVersion.txt

echo "Building LSP Version: $major.$minor.$patch on $current_date_time"

# Directory containing files with number suffixes
directory="LSPImages"

# Create a new file with the incremented suffix
dockerFileName="$directory/LSPImage-$LSPVersion.tar"

# Builds the LSP Docker Container
docker image build --build-arg LSP_ACCOUNT_NAME_VAR=${LSP_ACCOUNT_NAME} --tag $versionTag .

docker save $versionTag -o $dockerFileName

echo "Created $dockerFileName"
