/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: March 13th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal LSP Status App
 */

// C++ Include Files
#include <thread>
#include <iostream>
#include <string>
#include <sstream>
#include <fstream>
#include <cstring>
#include <ctime>

// log4cpp include files
#include <log4cxx/logger.h>

// Include the LSP Library header
#include "LSPAppLib.hpp"

#include "LSPStatusApp.h"

// EdgeIQ Call Home Endpoint
#define POST_CALL_HOME_URL "http://localhost:8084"

// The logger is used by the library code so it is declared and initialized there and extern here
log4cxx::LoggerPtr LSPAppLogger = LSPApp::getLogger("LSP Application");

using namespace std;

const std::string GetLSPStatusMessage(const LSPStatus &lspStatus)
{

    switch (lspStatus)
    {
    case LSPStatus::Unknown:
        return "Unknown state";
    case LSPStatus::LSPDisabled:
        return "LSP disabled state";
    case LSPStatus::CalledHome:
        return "Called home state";
    case LSPStatus::MissingSN:
        return "Missing serial number state";
    case LSPStatus::MissingLHData:
        return "Missing LH data state";
    case LSPStatus::EnrollmentTriggered:
        return "Enrollment triggered state";
    case LSPStatus::Enrolling:
        return "Enrolling state";
    case LSPStatus::EnrollmentFailed:
        return "Enrollment failed state";
    case LSPStatus::Enrolled:
        return "Enrolled state";
    case LSPStatus::EdgeIQUnreachable:
        return "EdgeIQ unreachable state";
    case LSPStatus::SFUnreachable:
        return "SF unreachable state";
    default:
        return "Invalid state";
    }
}

/**
 * @brief Triggers a "LSP Call Home" action for the device.
 *
 * This function initiates a "LSP Call Home" action by sending a request to a predefined URL. The
 * URL Endpoint is defined on the EdgeIQ cloud and is currently configured to be Port TCP/8084.
 *
 * It periodically retries the request for a total duration, initially retrying every 10 seconds
 * for the first 2 minutes, then increasing the interval to 30 seconds. The serial number is no
 * longer sent with the request, as EdgeIQ now relies on the device's certificate subject for
 * identification.
 *
 * @return Returns 0 if the "Call Home" action is successfully triggered. If an exception occurs
 * during the request process, the function logs the error and continues to retry. If the loop
 * exits without a successful request (which theoretically should not happen given the infinite loop),
 * it returns -1.
 *
 * @note The function logs the status of the automatic call home feature at the start, as well as
 * informational messages about retry intervals and the initiation of the "Call Home" action. It logs
 * any exceptions caught during the request process as fatal errors.
 *
 * @warning The function enters an infinite loop, relying on the successful completion of the request
 * or an external condition to break the loop. This design assumes that the "Call Home" action will
 * eventually succeed or that the device will otherwise intervene. It is essential to monitor the
 * behavior of this function to prevent indefinite operation in the case of persistent failures.
 */

int SendLSPStatus(const std::string &serialNumber, const LSPStatus &lspStatus)
{

    // Get the current date and time
    auto now = std::chrono::system_clock::now();
    auto now_time = std::chrono::system_clock::to_time_t(now);
    std::string dateTime = std::ctime(&now_time);
    dateTime.pop_back(); // Remove the trailing newline character

    json LSPStatusJSON = {
        {"deviceStatus", {{"serialNumber", serialNumber}, {"dateTime", dateTime}, {"status", "EnrollmentTriggered"}, {"statusMessage", GetLSPStatusMessage(lspStatus)}}}};

    const std::string callHomeURL = std::string(POST_CALL_HOME_URL);

    const int totalWaitTime = 2 * 60; // 2 minutes
    unsigned int waitTimeSeconds = 10;
    unsigned int totalSeconds = 0;

    LOG4CXX_INFO(LSPAppLogger, "Sending " << LSPStatusJSON.dump());

    while (true)
    {
        try
        {
            // Wait 10 seconds for EdgeIQ to connect
            std::this_thread::sleep_for(std::chrono::seconds(waitTimeSeconds));
            totalSeconds += waitTimeSeconds;

            if (totalSeconds > totalWaitTime)
            {
                waitTimeSeconds = 30; // Backoff to trying every 30 seconds
                LOG4CXX_INFO(LSPAppLogger, "Retry every " << waitTimeSeconds << " seconds");
            }

            LOG4CXX_INFO(LSPAppLogger, "Sending JSON Status");

            std::string response = LSPApp::postJSONToURL(callHomeURL, LSPStatusJSON.dump());

            LOG4CXX_DEBUG(LSPAppLogger, "Sending JSON Status: " + response);

            return 0;
        }
        catch (const std::exception &exc)
        {
            // catch anything thrown within try block that derives from std::exception
            LOG4CXX_FATAL(LSPAppLogger, exc.what());
        }
    }
    return -1;
}

const std::string GetDeviceSerialNumber(void)
{
    // First try the legacy method for backwards compatibility
    try
    {
        const std::string legacySMFile = "/opt/LSP/sys/firmware/vpd/ro/serial_number";

        // Try to read the serial number from the OM family location
        const std::string OMSerialNumber = LSPApp::ReadStringFromFile(legacySMFile);
        if (!OMSerialNumber.empty())
            return OMSerialNumber;

        throw std::runtime_error("Could not find serial number in " + legacySMFile);
    }

    // Couldn't open the serial number from the OM family location
    catch (const std::exception &exc)
    {
        const char *SerialNumberEnv = std::getenv("SERIAL_NUMBER"); // Read the serial number from the environment variable

        if (SerialNumberEnv)
            return (std::string(SerialNumberEnv)); // Convert to std::string

        throw std::runtime_error("Could not find serial number in SERIAL_NUMBER env var");
    }
}

/**
 * @brief The main app
 *
 */
int main(int argc, char *argv[])
{
    try
    {

        LOG4CXX_INFO(LSPAppLogger, "LSP Status App");

        const std::string serialNumber = GetDeviceSerialNumber();

        SendLSPStatus(serialNumber, LSPStatus::Enrolling);

        LOG4CXX_INFO(LSPAppLogger, "LSP Application exiting");

        return 0; // Success
    }
    catch (const std::exception &exc)
    {
        // catch anything thrown within try block that derives from std::exception
        LOG4CXX_FATAL(LSPAppLogger, exc.what());

        return -1; // Failure
    }
}
