/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: February 14th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal NGCS Application
 */

#ifndef LSPStatusApp_H // include guard
#define LSPStatusApp_H

enum class LSPStatus
{
    Unknown,
    LSPDisabled,
    CalledHome,
    MissingSN,
    MissingLHData,
    EnrollmentTriggered,
    Enrolling,
    EnrollmentFailed,
    Enrolled,
    EdgeIQUnreachable,
    SFUnreachable
};

#endif // LSPStatusApp_H
