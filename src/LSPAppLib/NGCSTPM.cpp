/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: March 13th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * LSP: NGCS TPM Abstraction
 */

// C++ Include Files
#include <string>
#include <vector>
#include <iostream>
#include <fstream>
#include <sstream>
#include <stdexcept>

// TSS Stack Includes for TPM
#include <tss2/tss2_rc.h>
#include <tss2/tss2_sys.h>
#include <tss2/tss2_tcti_device.h>

// Include the LSP Library header
#include "LSPAppLib.hpp"

// Include the TPM handle info
#include "TPMConfig.h"

// Include the interface for this class
#include "NGCSTPM.h"

#define TSS2_RC_FAILURE ((TSS2_RC)(0x1001))

// The logger is used by the library code so it is declared and initialized there and extern here
extern log4cxx::LoggerPtr LSPAppLogger;

using namespace std;

/**
 * @brief Constructs an NGCS TPM Abstraction instance.
 *
 */

NGCSTPM::NGCSTPM(void)
{

    tctiContext = nullptr;
    sysContext = nullptr;
}

bool NGCSTPM::PlatformHasTPM(void)
{

    return LSPApp::FileExists("/dev/tpmrm0");
}

// The TCTI API is very old school. Lots of back and forth to allocate and free memory, initialize objects and fill in values
void NGCSTPM::InitializeTPM(void)
{

    if ((tctiContext != nullptr) && (sysContext != nullptr))
        return;

    // Initialize TCTI context, binding to the TPM broker device
    TSS2_RC rc = initializeTctiContext("/dev/tpmrm0");
    if (rc != TSS2_RC_SUCCESS)
    {
        std::ostringstream message;
        message << "Failed to initialize TCTI context: rc 0x" << std::hex << rc << std::dec;
        throw std::runtime_error(message.str());
    }

    // Get the size of the TSS2_SYS_CONTEXT structure
    size_t contextSize = Tss2_Sys_GetContextSize(0);
    if (contextSize == 0)
    {
        free(tctiContext);
        throw std::runtime_error("Failed to get TSS2_SYS_CONTEXT size");
    }

    // Allocate memory for the TSS2_SYS_CONTEXT structure
    sysContext = (TSS2_SYS_CONTEXT *)calloc(1, contextSize);
    if (sysContext == nullptr)
    {
        free(tctiContext);
        throw std::runtime_error("Failed to allocate space for TSS2_SYS_CONTEXT");
    }

    // Initialize the TSS2_SYS_CONTEXT
    rc = Tss2_Sys_Initialize(sysContext, contextSize, tctiContext, nullptr);
    if (rc != TSS2_RC_SUCCESS)
    {
        free(tctiContext);
        std::ostringstream message;
        message << "Failed to initialize TSS2_SYS_CONTEXT. rc 0x" << std::hex << rc << std::dec;
        throw std::runtime_error(message.str());
    }

    // LOG4CXX_INFO(LSPAppLogger, "TPM Abstraction is initialized");
}

/**
 * @brief Destroys the NGCS TPM Abstraction instance
 *
 */
NGCSTPM::~NGCSTPM(void)
{

    // Clean up
    if (sysContext != nullptr)
    {
        Tss2_Sys_Finalize(sysContext);
        sysContext = nullptr;
    }

    if (tctiContext != nullptr)
    {
        free(tctiContext);
        tctiContext = nullptr;
    }
}

// Function to get the authorization session object.
// We don't use authentication but you have to define a NULL auth context
TSS2L_SYS_AUTH_COMMAND *NGCSTPM::getCmdAuth(void)
{

    static TSS2L_SYS_AUTH_COMMAND authCmd;

    // Initialize the authorization session object
    authCmd.count = 1;
    authCmd.auths[0].sessionHandle = TPM2_RS_PW; // Use the owner authorization session handle
    authCmd.auths[0].nonce.size = 0;             // No nonce required
    authCmd.auths[0].sessionAttributes = 0;      // No special session attributes
    authCmd.auths[0].hmac.size = 0;              // No HMAC required

    return &authCmd;
}

TSS2_RC NGCSTPM::initializeTctiContext(const char *socketPath)
{

    size_t contextSize = 0;

    TSS2_RC rc = Tss2_Tcti_Device_Init(NULL, &contextSize, NULL);
    if (rc != TSS2_RC_SUCCESS)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to get size for device TCTI context: 0x" << std::hex << rc << std::dec);
        return rc;
    }

    tctiContext = (TSS2_TCTI_CONTEXT *)calloc(1, contextSize);
    if (tctiContext == nullptr)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Allocation for TCTI context failed");
        return TSS2_RC_FAILURE;
    }

    rc = Tss2_Tcti_Device_Init(tctiContext, &contextSize, socketPath);
    if (rc != TSS2_RC_SUCCESS)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to initialize device TCTI context: 0x" << std::hex << rc << std::dec);
        return rc;
    }

    return TSS2_RC_SUCCESS;
}

// Function to read a TPM property value
TPM2_RC NGCSTPM::readTPMProperty(const TPM2_PT property, UINT32 *propertyValue)
{

    TPMS_CAPABILITY_DATA capabilityData;
    TPMI_YES_NO moreData;

    // Call TPM2_GetCapability to retrieve the property
    TPM2_RC rc = Tss2_Sys_GetCapability(sysContext, NULL, TPM2_CAP_TPM_PROPERTIES, property, 1, &moreData, &capabilityData, NULL);
    if (rc != TPM2_RC_SUCCESS)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to get TPM capability: " << std::hex << rc << std::dec);
        return rc;
    }

    // Retrieve the property value from the capability data
    *propertyValue = capabilityData.data.tpmProperties.tpmProperty[0].value;

    return TPM2_RC_SUCCESS;
}

UINT32 NGCSTPM::getTPMPropertyValue(const TPM2_PT property)
{

    UINT32 value = 0;

    // Read TPM property TPM2_PT_NV_BUFFER_MAX
    TSS2_RC rc = readTPMProperty(property, &value);
    if (rc != TPM2_RC_SUCCESS)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to read TPM property TPM2_PT_NV_BUFFER_MAX: " << std::hex << rc << std::dec);
        throw std::runtime_error("Failed to read TPM property TPM2_PT_NV_BUFFER_MAX");
    }

    return value;
}

// Function to read the size of the NV object in the TPM
size_t NGCSTPM::getObjectSize(const TPMI_RH_NV_INDEX nvIndex)
{

    TPM2B_NV_PUBLIC nvPublic = {0};

    // Query the size of the data in the NV index
    TSS2_RC rc = Tss2_Sys_NV_ReadPublic(sysContext, nvIndex, nullptr, &nvPublic, nullptr, nullptr);
    if (rc != TSS2_RC_SUCCESS)
    {
        LOG4CXX_ERROR(LSPAppLogger, "ailed to read object size for object at address " << std::hex << nvIndex << std::dec);
        throw std::runtime_error("Failed to read object size");
    }

    return nvPublic.nvPublic.dataSize;
}

std::vector<uint8_t> NGCSTPM::readObjectFromNVRAM(const TPMI_RH_NV_INDEX nvIndex)
{

    // Get the size of the object so we can read it out
    size_t objectSize = getObjectSize(nvIndex);

    // TPM has a max buffer size limit. Get the limit from the TPM and use it to read the data in chunks
    UINT32 maxNVRAMBuffer = getTPMPropertyValue(TPM2_PT_NV_BUFFER_MAX);

    // Get the authorization session object
    TSS2L_SYS_AUTH_COMMAND *cmdAuth = getCmdAuth();

    // Vector to store the data read from successive reads
    std::vector<uint8_t> objectData;

    // Loop to read NV data in chunks of maxNVRamBuffer bytes
    size_t bytesRead = 0;
    while (bytesRead < objectSize)
    {
        size_t bytesToRead = std::min((size_t)maxNVRAMBuffer, objectSize - bytesRead);

        TPM2B_MAX_NV_BUFFER nvData;

        // Read data from the NV index
        TSS2_RC rc = Tss2_Sys_NV_Read(sysContext, TPM2_RH_OWNER, nvIndex, cmdAuth, bytesToRead, bytesRead, &nvData, NULL);

        if (rc != TSS2_RC_SUCCESS)
        {
            // Handle error: failed to read NV index
            throw std::runtime_error("Failed to read TPM NV index");
        }

        // Append the read data to the objectData vector
        objectData.insert(objectData.end(), nvData.buffer, nvData.buffer + bytesToRead);

        // Update bytesRead
        bytesRead += bytesToRead;
    }

    return objectData;
}

TSS2_RC NGCSTPM::undefineNVRAMArea(const TPMI_RH_NV_INDEX nvIndex)
{

    TSS2L_SYS_AUTH_COMMAND *cmdAuth = getCmdAuth();

    // Invoke the TPM2_NV_UndefineSpace command
    TSS2_RC rc = Tss2_Sys_NV_UndefineSpace(sysContext, TPM2_RH_OWNER, nvIndex, cmdAuth, NULL);
    if (rc != TSS2_RC_SUCCESS)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to undefine NV index: 0x" << std::hex << rc << std::dec);
        throw std::runtime_error("Failed to undefine NV index");
    }

    return TPM2_RC_SUCCESS;
}

TSS2_RC NGCSTPM::defineNVRAMArea(const TPMI_RH_NV_INDEX nvIndex, const size_t desiredNVRAMSize)
{

    // Prepare NV public data
    TPM2B_NV_PUBLIC nvPublic = {
        .nvPublic = {
            .nvIndex = nvIndex, // NVRAM Address
            .nameAlg = TPM2_ALG_SHA256,
            .attributes = TPMA_NV_OWNERREAD | TPMA_NV_OWNERWRITE,
            .authPolicy = {.size = 0},
            .dataSize = (UINT16)desiredNVRAMSize}};

    TSS2L_SYS_AUTH_COMMAND *cmdAuth = getCmdAuth();

    // Define the size of the space associated with the NV index
    TSS2_RC rc = Tss2_Sys_NV_DefineSpace(sysContext, TPM2_RH_OWNER, cmdAuth, nullptr, &nvPublic, nullptr);
    if (rc != TSS2_RC_SUCCESS)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to define NV index: 0x" << std::hex << rc << std::dec);
        return rc;
    }

    return TSS2_RC_SUCCESS;
}

TSS2_RC NGCSTPM::writeCertificateToNVRAM(const TPMI_RH_NV_INDEX nvIndex, const std::string &certificate)
{

    // TPM has a max buffer size limit. Get the limit from the TPM and use it to read the data in chunks
    UINT32 maxNVRAMBuffer = getTPMPropertyValue(TPM2_PT_NV_BUFFER_MAX);

    TSS2L_SYS_AUTH_COMMAND *cmdAuth = getCmdAuth();

    size_t totalSize = certificate.size();
    size_t bytesRemaining = certificate.size();
    size_t offset = 0;

    // Write the data in chunks smaller than the NV_BUFFER_MAX
    while (bytesRemaining > 0)
    {

        const size_t chunkSize = min(bytesRemaining, (size_t)maxNVRAMBuffer);

        // Prepare NV write data
        TPM2B_MAX_NV_BUFFER nvData;

        nvData.size = chunkSize;
        if (nvData.size > sizeof(nvData.buffer))
        {
            LOG4CXX_ERROR(LSPAppLogger, "Certificate size exceeds maximum NV buffer size");
            return TSS2_RC_FAILURE;
        }

        // Copy the data into the buffer in chunks
        memcpy(nvData.buffer, certificate.c_str() + offset, nvData.size);

        // LOG4CXX_INFO(LSPAppLogger, "Chunk size " << chunkSize
        //              << " offset " << offset
        //              << "  bytes remaining " << bytesRemaining);

        // Write data to the NV index
        TSS2_RC rc = Tss2_Sys_NV_Write(sysContext, TPM2_RH_OWNER, nvIndex, cmdAuth, &nvData, offset, nullptr);
        if (rc != TSS2_RC_SUCCESS)
        {
            LOG4CXX_ERROR(LSPAppLogger, "Failed to write data to NV index: 0x%x\n"
                                            << rc);
            return rc;
        }

        // Adjust the sizes
        bytesRemaining -= chunkSize;
        offset += chunkSize;
    }

    return TSS2_RC_SUCCESS;
}

std::string NGCSTPM::GetLSPCertificateFromTPM(const unsigned long nvIndexHandle)
{

    try
    {

        InitializeTPM();

        // Read the LSP certificate from the TPM at the aaddress specified
        vector<uint8_t> LSPCertificateData = readObjectFromNVRAM(nvIndexHandle);

        if (LSPCertificateData.empty())
            throw std::runtime_error("Failed to read LSP Certificate from TPM");

        return std::string(LSPCertificateData.begin(), LSPCertificateData.end());
    }
    catch (const std::exception &exc)
    {
        // catch anything thrown within try block that derives from std::exception
        LOG4CXX_FATAL(LSPAppLogger, exc.what());
        throw exc;
    }
}

// The TCTI API is old school. Lots of back and forth to allocate and free memory, initialize objects and fill in structure values
int NGCSTPM::writeCertificateToTPM(const std::string &certificate, const unsigned long nvIndexHandle)
{

    InitializeTPM();

    // Check if there is an existing object at the address we are using and remove it if there is
    size_t objectSize = getObjectSize(nvIndexHandle);
    if (objectSize)
    {
        // Remove the existing certificate
        TSS2_RC rc = undefineNVRAMArea(nvIndexHandle);
        if (rc != TSS2_RC_SUCCESS)
        {
            throw std::runtime_error("Failed to remove LSP Certificate from TPM");
        }
    }

    // Define the size for the NV index
    TSS2_RC rc = defineNVRAMArea(nvIndexHandle, certificate.size());
    if (rc != TSS2_RC_SUCCESS)
    {
        throw std::runtime_error("Failed to define memory to store LSP Certificate in TPM");
    }

    LOG4CXX_INFO(LSPAppLogger, "TPM NV RAM was defined for storing the LSP Device Certificate");

    // Write data to NV index
    rc = writeCertificateToNVRAM(nvIndexHandle, certificate);
    if (rc != TSS2_RC_SUCCESS)
    {
        throw std::runtime_error("Failed to store LSP Certificate in TPM");
    }

    LOG4CXX_INFO(LSPAppLogger, "LSP Device Certificate was written to the TPM");

    return EXIT_SUCCESS;
}
