/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: February 14th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal NGCS Application
 */

// C Include files
#include <sys/stat.h>

// C++ Include Files
#include <string>
#include <sstream>
#include <fstream>

// log4cpp include files
#include <log4cxx/logger.h>
#include <log4cxx/xml/domconfigurator.h>

// CURL Library C++ wrapper Include Files
#include <curlpp/cURLpp.hpp>
#include <curlpp/Easy.hpp>
#include <curlpp/Options.hpp>
#include <curlpp/Exception.hpp>
#include <curlpp/Infos.hpp>

// JSON for C++
#include "nlohmann/json.hpp"

// libssh C wrapper
#include <libssh/libssh.h>
#include <libssh/callbacks.h>

// Include file with headers for this library
#include "LSPAppLib.hpp"

// Define static logger variable
using namespace log4cxx;
using namespace log4cxx::xml;
using namespace log4cxx::helpers;

using LoggerPtr = log4cxx::LoggerPtr;

// Default NGCS device IP address
std::string hostDockerIPAddr = NGCS_HOST_DOCKER_IP;

// We are using curl with the c++ wrapper
using namespace curlpp::options;

// And json for Modern c++
using json = nlohmann::json;

// Define a global variable with a constructor that contains the initialzer for the logger
// This ensures the logging system is initialzed before any other code executes
struct Log4cppInitializer
{

    Log4cppInitializer()
    {
        // Load Logging system XML configuration file using DOMConfigurator

        // Get the value of the environment variable LOG4CXX_CONFIGURATION
        const char *configFilePath = std::getenv("LOG4CXX_CONFIGURATION");

        if (configFilePath != nullptr)
        {
            // Configure Log4cxx using the environment variable value
            log4cxx::xml::DOMConfigurator::configure(configFilePath);
        }
        else
        {
            // Default configuration if the environment variable is not set
            log4cxx::xml::DOMConfigurator::configure("Log4cxxConfig.xml");
        }
    }
};

// Define a global instance of the Logging Initializer so that the logging system is initialized before any other code executes
static Log4cppInitializer log4cppInitializer;

namespace LSPApp
{

    const std::string getHostDockerIPAddr(void)
    {
        return hostDockerIPAddr;
    }

    void setHostDockerIPAddr(const std::string &hostIPAddr)
    {
        hostDockerIPAddr = hostIPAddr;
    }

    // Function implementation
    log4cxx::LoggerPtr getLogger(const std::string &appName)
    {

        // Append the application name to a base logger name
        std::string loggerName = "LSPApp." + appName;

        return log4cxx::Logger::getLogger(loggerName);
    }

    // Helper function to return a string JSON value for the key
    std::string findJSONValueByKey(const json &item, const std::string &key)
    {

        if (item.find(key) != item.end())
        {
            return item[key];
        }

        return "";
    }

    // Helper function to return a bool JSON value for the key
    bool findJSONBoolByKey(const json &item, const std::string &key)
    {

        if (item.find(key) != item.end())
        {
            return item[key];
        }

        return false;
    }

    /**
     * @brief Checks if a file exists at the specified file path.
     *
     * This function determines the existence of a file by attempting to retrieve its
     * status information using the `stat` system call. It is a lightweight and efficient
     * way to check for a file's existence without opening the file.
     *
     * @param filePath The path to the file whose existence needs to be checked. This should
     * be a fully qualified path or a relative path from the current working directory of the
     * application.
     *
     * @return bool Returns `true` if the file exists at the specified path, `false` otherwise.
     */

    bool FileExists(const std::string filePath)
    {

        struct stat buffer;
        return (stat(filePath.c_str(), &buffer) == 0);
    }

    // Read a string from a file
    std::string ReadStringFromFile(std::string filePath)
    {

        std::ifstream file(filePath);

        if (!file.is_open())
        {
            throw std::runtime_error("Could not open file: " + filePath);
        }

        // Read the contents of the file into a stringstream
        std::stringstream buffer;
        buffer << file.rdbuf();

        // Close the file
        file.close();

        std::string fileContent = buffer.str();

        // Remove trailing \n
        if (!fileContent.empty() && fileContent.back() == '\n')
            fileContent.pop_back();

        return fileContent;
    }

    /**
     * @brief Function to write a string to a file.
     *
     * This function writes the given string content to the specified file path.
     *
     * @param content The string content to be written to the file.
     * @param filePath The path of the file to write the content to.
     * @param descripton Description of the file being written.
     */

    void WriteStringToFile(const std::string &content, const std::string &filePath, const std::string descripton)
    {

        std::ofstream outFile(filePath);

        if (!outFile.is_open())
        {
            throw std::runtime_error("Failed to write " + descripton + ": " + filePath);
        }

        outFile << content;
        outFile.close();
    }

    const std::string postJSONToURL(const std::string &URL,
                                    const std::string &jsonPayload,
                                    const std::string &session)
    {
        try
        {
            curlpp::Cleanup cleaner;
            curlpp::Easy request;

            // Set URL and options
            request.setOpt(curlpp::options::Url(URL));
            request.setOpt(curlpp::options::Verbose(true)); // Enable verbose output for debugging
            request.setOpt(curlpp::options::SslVerifyPeer(false));
            request.setOpt(curlpp::options::SslVerifyHost(false));

            // Set headers
            std::list<std::string> headers;
            headers.push_back("Accept: application/json");
            headers.push_back("Content-Type: application/json");

            // Add authentication token, if specified
            if (!session.empty())
            {
                headers.push_back("Authorization: Token " + session);
            }

            request.setOpt(curlpp::options::HttpHeader(headers));

            // Set POST fields
            request.setOpt(curlpp::options::PostFields(jsonPayload.c_str()));
            request.setOpt(curlpp::options::PostFieldSize(jsonPayload.size()));

            // Write response to string stream
            std::ostringstream os;
            request.setOpt(curlpp::options::WriteStream(&os));

            // Perform the request
            request.perform();

            // Check HTTP response code
            long httpCode = curlpp::infos::ResponseCode::get(request);
            if (httpCode != 200)
            {
                LOG4CXX_WARN(LSPApp::getLogger("LSP App"), "POST to URL " + URL + " returned HTTP Response Code: " << httpCode);
                throw std::runtime_error("POST to URL returned: " + httpCode);
            }

            // Return the response as a string
            return os.str();
        }
        catch (curlpp::LogicError &e)
        {
            LOG4CXX_ERROR(LSPApp::getLogger("LSP App"), e.what());

            throw e;
        }
        catch (curlpp::RuntimeError &e)
        {
            LOG4CXX_ERROR(LSPApp::getLogger("LSP App"), e.what());

            throw e;
        }
    }

    // Submit JSON to the specified URL using PUT method with session token in HTTP Authentication header
    const std::string putJSONToURL(const std::string &URL,
                                   const std::string &jsonPayload,
                                   const std::string &session = std::string())
    {

        try
        {

            // Initialize curlpp
            curlpp::Cleanup cleaner;
            curlpp::Easy request;

            // Set common options
            request.setOpt(new curlpp::options::Url(URL));
            request.setOpt(new curlpp::options::Verbose(false));
            request.setOpt(new curlpp::options::SslVerifyPeer(false));
            request.setOpt(new curlpp::options::SslVerifyHost(false));

            // Set headers
            std::list<std::string> headers;
            headers.push_back("Accept: application/json");
            headers.push_back("Content-Type: application/json");

            // Add authentication token, if specified
            if (!session.empty())
            {
                headers.push_back("Authorization: Token " + session);
            }
            request.setOpt(new curlpp::options::HttpHeader(headers));

            // Use the PUT method
            request.setOpt(curlpp::Options::CustomRequest("PUT"));

            // curlcpp API uses the Post Fields to set the payload for PUT
            request.setOpt(new curlpp::options::PostFields(jsonPayload.c_str()));
            request.setOpt(new curlpp::options::PostFieldSize(jsonPayload.size()));

            // Perform the request
            std::ostringstream os;
            curlpp::options::WriteStream ws(&os);

            request.setOpt(ws);
            request.perform();

            // Check HTTP response code
            long httpCode = curlpp::infos::ResponseCode::get(request);
            if (httpCode != 200)
            {
                LOG4CXX_WARN(LSPApp::getLogger("LSP App"), "POST to URL " + URL + " returned HTTP Response Code: " << httpCode);
                throw std::runtime_error("POST to URL returned: " + httpCode);
            }

            return os.str();
        }
        catch (curlpp::LogicError &e)
        {
            LOG4CXX_ERROR(LSPApp::getLogger("LSP App"), e.what());

            throw e;
        }
        catch (curlpp::RuntimeError &e)
        {
            LOG4CXX_ERROR(LSPApp::getLogger("LSP App"), e.what());

            throw e;
        }
        catch (const std::exception &e)
        {
            // catch anything thrown within try block that derives from std::exception
            LOG4CXX_ERROR(LSPApp::getLogger("LSP App"), e.what());

            throw e;
        }
    }

    // Get JSON response from the specified REST API endppint
    const std::string getJSONFromURL(const std::string &URL,
                                     const std::string &session = std::string())
    {

        try
        {

            curlpp::Cleanup cleaner;
            curlpp::Easy request;

            request.setOpt(new curlpp::options::Url(URL));
            request.setOpt(new curlpp::options::Verbose(false));
            request.setOpt(new curlpp::options::SslVerifyPeer(false));
            request.setOpt(new curlpp::options::SslVerifyHost(false));

            std::list<std::string> headers;
            headers.push_back("Accept: application/json");
            headers.push_back("Content-Type: application/json");

            // Add authentication token, if specifed
            if (!session.empty())
            {
                headers.push_back("Authorization: Token " + session);
            }

            request.setOpt(new curlpp::options::HttpHeader(headers));

            std::ostringstream os;
            curlpp::options::WriteStream ws(&os);

            request.setOpt(ws);
            request.perform();

            // Check HTTP response code
            long httpCode = curlpp::infos::ResponseCode::get(request);
            if (httpCode != 200)
            {
                LOG4CXX_WARN(LSPApp::getLogger("LSP App"), "GET from URL " + URL + " returned HTTP Response Code: " << httpCode);
                throw std::runtime_error("GET from returned: " + httpCode);
            }

            return os.str();
        }
        catch (curlpp::LogicError &e)
        {
            LOG4CXX_ERROR(LSPApp::getLogger("LSP App"), e.what());

            throw e;
        }
        catch (curlpp::RuntimeError &e)
        {
            LOG4CXX_ERROR(LSPApp::getLogger("LSP App"), e.what());

            throw e;
        }
        catch (const std::exception &e)
        {
            // catch anything thrown within try block that derives from std::exception
            LOG4CXX_ERROR(LSPApp::getLogger("LSP App"), e.what());

            throw e;
        }
    }

    /**
     * @brief Retrieves the next command-line argument, if available.
     *
     * This function is designed to aid in parsing command-line arguments by fetching
     * the argument immediately following a specified option. It ensures that there
     * is an argument available after the current index and updates the index to skip
     * the fetched argument, preventing it from being processed again.
     *
     * @param index Reference to the current index in the argv array being processed. This
     * index is updated to skip the next argument after it is successfully fetched.
     * @param argc The number of command-line arguments passed to the application.
     * @param argv The array of command-line arguments passed to the application.
     * @param nextArg Reference to a string where the next argument will be stored if
     * successfully fetched.
     *
     * @return Returns true if the next argument is successfully fetched and stored in
     * `nextArg`. Returns false if there are no more arguments available after the current
     * index, indicating a missing argument for an option.
     *
     * @note This function is typically used in a loop to process all provided command-line
     * arguments. It's especially useful for options that require an accompanying value.
     *
     * @warning If a required argument for an option is missing, the function logs an error
     * message indicating which option is missing its argument. This could potentially halt
     * further argument processing, depending on the application's error handling strategy.
     */

    bool getNextArg(int &index, int argc, char *argv[], std::string &nextArg)
    {
        if (index + 1 < argc)
        {
            nextArg = argv[index + 1];
            index++; // Skip next argument since it's already consumed

            return true;
        }
        else
        {
            LOG4CXX_ERROR(LSPApp::getLogger("LSP App"), argv[index] << " option requires one argument.");
            return false;
        }
    }

    unsigned long parseHexString(const std::string &hexString)
    {
        std::string sanitizedHexString = hexString;

        // Remove the "0x" prefix
        if (sanitizedHexString.size() > 2 && sanitizedHexString.substr(0, 2) == "0x")
        {
            sanitizedHexString = sanitizedHexString.substr(2);
        }

        // Parse the string as a hexadecimal number
        unsigned long intValue;
        std::stringstream ss;
        ss << std::hex << sanitizedHexString;
        ss >> intValue;

        return intValue;
    }

} // namespace LSPApp
