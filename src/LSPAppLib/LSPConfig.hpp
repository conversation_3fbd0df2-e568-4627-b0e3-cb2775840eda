/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: February 14th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal NGCS Application
 */

#ifndef LSPConfig_HPP // include guard
#define LSPConfig_HPP

// Username for SSH operations from docker to the host
#define NGCS_ROOT_USERNAME "root"

// LSP Directories seen from Docker and mounted from the host
#define NGCS_LSP_IDENTITY "/opt/LSP/keys/LSP_docker"
#define LSP_CERTS_DIR "/opt/LSP/pki"

// Used to signal this app when we receive the enrollment data
#define LSP_ENROLLMENT_FIFO "/opt/LSP/conf/enrollmentfifo"

// NGCS Host Docker interface IP address
#define NGCS_HOST_DOCKER_IP "**********"

// NGCS REST API Base Endpoint
#define NGCS_REST_API_BASE ":443/api/v2/"

// NGCS Lighthouse Physical Interfaces Endpoint
#define POST_PHYSIF_URL "physifs"

// NGCS Serial number Endpoint
#define GET_SERIAL_NUM_URL "system/serial_number"

// NGCS Lighthouse Enrollment Endpoint
#define GET_LH_ENROLLMENTS_URL "lighthouse_enrollments"

// NGCS Lighthouse Enrollment Endpoint
#define POST_ENROLLMENT_URL "lighthouse_enrollments"

#endif // LSConfig_HPP
