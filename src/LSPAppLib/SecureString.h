#ifndef SECURESTRING_H
#define SECURESTRING_H

#include <vector>
#include <string>
#include <cstring>

#include <openssl/crypto.h> // For OPENSSL_cleanse

class SecureString
{
public:
    SecureString() = default;

    explicit SecureString(const std::string &str) : data(str.begin(), str.end()) {}
    explicit SecureString(const char *str) : data(str, str + std::strlen(str)) {}

    std::string toString() const
    {
        return std::string(data.begin(), data.end());
    }

    ~SecureString()
    {
        OPENSSL_cleanse(data.data(), data.size());
    }

    // Copy and assignment are not safe for SecureString due to the sensitive nature of its contents
    SecureString(const SecureString &) = delete;
    SecureString &operator=(const SecureString &) = delete;

    // Implement move semantics to allow SecureString to be moved
    SecureString(SecureString &&) noexcept = default;
    SecureString &operator=(SecureString &&) noexcept = default;

private:
    std::vector<char> data;
};

#endif // SECURESTRING_H
