#ifndef NGCSTPM_H
#define NGCSTPM_H

// C++ Include Files
#include <string>
#include <vector>

// TSS Stack Includes for TPM
#include <tss2/tss2_rc.h>
#include <tss2/tss2_sys.h>
#include <tss2/tss2_tcti_device.h>

class NGCSTPM
{

public:
  // CTOR
  NGCSTPM(void);

  bool PlatformHasTPM(void);

  // Writes a PEM certificate to the TPM NV RAM at the address specified
  int writeCertificateToTPM(const std::string &certificate, const unsigned long nvIndexHandle);

  // Reads a PEM certificate from the TPM NV RAM at the address specified
  std::string GetLSPCertificateFromTPM(const unsigned long nvIndexHandle);

  ~NGCSTPM(void);

private:
  TSS2_TCTI_CONTEXT *tctiContext;
  TSS2_SYS_CONTEXT *sysContext;

  void InitializeTPM(void);

  TSS2_RC initializeTctiContext(const char *socketPath);

  static TSS2L_SYS_AUTH_COMMAND *getCmdAuth(void);

  TSS2_RC writeCertificateToNVRAM(const TPMI_RH_NV_INDEX nvIndex, const std::string &certificate);

  bool isObjectStoredInTPM(const TPMI_RH_NV_INDEX nvIndex);

  size_t getObjectSize(const TPMI_RH_NV_INDEX nvIndex);

  std::vector<uint8_t> readObjectFromNVRAM(const TPMI_RH_NV_INDEX nvIndex);

  TSS2_RC undefineNVRAMArea(const TPMI_RH_NV_INDEX nvIndex);

  TSS2_RC defineNVRAMArea(const TPMI_RH_NV_INDEX nvIndex, const size_t desiredNVRAMSize);

  UINT32 getTPMPropertyValue(const TPM2_PT property);

  TPM2_RC readTPMProperty(const TPM2_PT property, UINT32 *propertyValue);
};

#endif // NGCSTPM_H
