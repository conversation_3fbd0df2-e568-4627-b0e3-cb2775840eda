/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: February 14th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal NGCS Application
 */

#ifndef LSPApp_HPP // include guard
#define LSPApp_HPP

// log4cpp include files
#include <log4cxx/logger.h>

// JSON for C++
#include "nlohmann/json.hpp"

// All the LSP Application configuration is here
#include "LSPConfig.hpp"

// We use json for Modern c++
using json = nlohmann::json;

namespace LSPApp
{
    const std::string getHostDockerIPAddr();

    void setHostDockerIPAddr(const std::string &hostIPAddr);

    // void enableVerboseSSHLogs(void);

    // Not used by the normal app. Just for test and debug
    // void setSSHPasswordMode(const std::string & password);

    // Connect to host using SSH and get a REST API token. The SSH identity is set here to the default here
    // const std::string getSessionTokenUsingSSH(const std::string & sshIdentity = std::string(NGCS_LSP_IDENTITY));

    // Get JSON response from the specified REST API endppint
    const std::string getJSONFromURL(const std::string &URL,
                                     const std::string &session);

    // Submit JSON to the specified URL using POST method with session token in HTTP Authentication header
    const std::string postJSONToURL(const std::string &URL,
                                    const std::string &jsonPayload,
                                    const std::string &session = std::string());

    // Submit JSON to the specified URL using PUT method with session token in HTTP Authentication header
    const std::string putJSONToURL(const std::string &URL,
                                   const std::string &jsonPayload,
                                   const std::string &session);

    // Helper function to return a string JSON value for the key
    std::string findJSONValueByKey(const json &item, const std::string &key);

    // Helper function to return a bool JSON value for the key
    bool findJSONBoolByKey(const json &item, const std::string &key);

    // Function to get a logger with a custom name
    log4cxx::LoggerPtr getLogger(const std::string &appName);

    // Read a string from a file
    std::string ReadStringFromFile(std::string filePath);

    bool FileExists(const std::string filePath);

    // Write a string to a file
    void WriteStringToFile(const std::string &content, const std::string &filePath, const std::string descripton);

    bool getNextArg(int &index, int argc, char *argv[], std::string &nextArg);

    unsigned long parseHexString(const std::string &hexString);
}

#endif // LSPApp_HPP
