#ifndef AWS_CONFIG_H
#define AWS_CONFIG_H

#include <string>

struct AWSConfig
{
    static const std::string ENCRYPTED_ACCOUNT_ID;
    static const std::string ENCRYPTED_SESSION_TOKEN;
};

const std::string AWSConfig::ENCRYPTED_ACCOUNT_ID = "9umhFjFV1l1LaB9kpei6Xg==";
const std::string AWSConfig::ENCRYPTED_SESSION_TOKEN = "9umhFjFV1l1LaB9kpei6Xg==";

const Aws::String ProductionCAArn = "arn:aws:acm-pca:us-east-1:************:certificate-authority/7812afcb-feb2-4cd2-9e06-163fdef695b1";
const Aws::String StagingCAArn = "arn:aws:acm-pca:us-east-1:************:certificate-authority/3e45a8a1-4ec5-4ccb-97b7-ab582df66073";

#endif // AWS_CONFIG_H
