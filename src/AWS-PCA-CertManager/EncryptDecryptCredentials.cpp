/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: February 14th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal Certificate Handler Application
 */

// C++ Include Files
#include <iostream>
#include <string>
#include <fstream>
#include <cstring>
#include <sstream>

// JSON for C++
#include "nlohmann/json.hpp"

// Include the LSP Library header
#include "LSPAppLib.hpp"

// Include header files for classes that support this application
#include "AWSCertificateMgr.h"

// Use JSON for modern C++ library
using json = nlohmann::json;

std::string mode;

// The logger is used by the library code so it is declared and initialized here
log4cxx::LoggerPtr LSPAppLogger = LSPApp::getLogger("Cert App");

std::pair<std::string, std::string> readAwsCredentials(const std::string &filename)
{

    std::ifstream file(filename);

    if (!file.is_open())
    {
        throw std::runtime_error("Error: Unable to open file " + filename);
    }

    // Parse JSON from the file
    json jsonData;
    file >> jsonData;

    // Extract values from JSON
    std::string awsAccountId = jsonData["AWS_ACCOUNT_ID"];
    std::string awsSessionToken = jsonData["AWS_SESSION_TOKEN"];

    return std::make_pair(awsAccountId, awsSessionToken);
}

std::pair<std::string, std::string> readEncAwsCredentials(const std::string &filename)
{

    std::ifstream file(filename);

    if (!file.is_open())
    {
        throw std::runtime_error("Error: Unable to open file " + filename);
    }

    // Parse JSON from the file
    json jsonData;
    file >> jsonData;

    // Extract values from JSON
    std::string awsAccountId = jsonData["encryptedAccountID"];
    std::string awsSessionToken = jsonData["encryptedSessionToken"];

    return std::make_pair(awsAccountId, awsSessionToken);
}

static void show_usage(std::string name)
{
    std::cerr << "Usage: " << name << " "
              << "Options:\n"
              << std::endl
              << "\t-e,--encrypt AWS API token" << std::endl
              << "\t-d,--decrypt AWS API token" << std::endl
              << "\t-h,--help\t\tShow help\n"
              << std::endl;
}

// Process command line arguments
int handleArguments(int argc, char *argv[])
{

    // check each command line argument
    for (int i = 1; i < argc; ++i)
    {
        std::string arg = argv[i];

        if ((arg == "-h") || (arg == "--help"))
        {
            show_usage(argv[0]);
            return 1;
        }
        else if ((arg == "-e") || (arg == "--encrypt"))
            mode = "ENCRYPT";
        else if ((arg == "-d") || (arg == "--decrypt"))
            mode = "DECRYPT";
    }

    return 0;
}

int main(int argc, char *argv[])
{

    if (handleArguments(argc, argv) == 0)
    {

        AWSCertificateMgr AWSCertificateMgr;

        if (mode == "ENCRYPT")
        {
            auto credentials = readAwsCredentials("AWSCleartextCredentials.json");

            AWSCertificateMgr.encryptAWSAccountIDs(credentials.first, credentials.second, "AWSEncryptedCredentials.json");
        }

        if (mode == "DECRYPT")
        {
            auto encFromFile = readEncAwsCredentials("AWSEncryptedCredentials.json");

            AWSCertificateMgr.printDecryptedAWSAccountIDs(encFromFile.first, encFromFile.second);
        }
    }

    return 0;
}
