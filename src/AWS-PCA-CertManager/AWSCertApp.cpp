/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: February 14th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal Certificate Handler Application
 */

// C++ Include Files
#include <string>

// Include the LSP Library header
#include "LSPAppLib.hpp"

#include <openssl/provider.h>

// Include header files for classes that support this application
#include "AWSCertificateMgr.h"

// Default EdgeIQ domain - the LSP sub-account name is prepended when we create a CSR
const std::string rootDomain = "devices.edgeiq.ai";

// The logger is used by the library code so it is declared and initialized there and extern here
log4cxx::LoggerPtr LSPAppLogger = LSPApp::getLogger("Cert App");

using namespace std;

/**
 * @brief Checks if the device certificate exists and matches the specified serial number and domain.
 *
 * This function checks if the device certificate exists in the specified directory,
 * and verifies if the certificate's subject matches the provided serial number and domain.
 *
 * @param certsDirectory The directory containing the device certificate.
 * @param serialNumber The serial number to be checked against the certificate's subject.
 * @param domain The domain to be checked against the certificate's subject.
 * @return true if the device certificate exists and matches the specified serial number and domain, false otherwise.
 */

/**
 * @brief Device Certificate Check and Update
 *
 * This program checks the current SSL/TLS certificate for a specified device
 * to determine if it is still valid and can be used. If the certificate is
 * expired or otherwise invalid, the program will request a new certificate from
 * the Certificate Authority (CA). The program returns specific codes based on the
 * outcome of this check.
 *
 * @return int Returns 0 if the current certificate exists, has not expired, and  can be used
 *             Returns 1 if a new certificate was requested from the CA and is available
 *             Returns -1 if there was an error during the operation.
 *
 * @param argc The number of command-line arguments.
 * @param argv Array of command-line arguments. Expected arguments are:
 *        - argv[1]: The LSP Account name for the certificate
 *        - argv[2]: The device's serial number.
 *
 * Usage:
 *        ./programName account-name device-serial-number path-to-certs-dir
 */

int main(int argc, char *argv[])
{

  if (argc < 3)
  {
    // Print usage information
    std::cerr << "Usage: " << argv[0]
              << " account-name"
              << " device-serial-number "
              << std::endl;
    return 1;
  }

  // Account name, Serial number and path to certs dir are passed as an arguments
  const string domain = std::string(argv[1]) + "." + rootDomain;
  const string serialNumber = argv[2];

  AWSCertificateMgr AWSCertificateMgr;

  return (int)AWSCertificateMgr.CheckDeviceCertificateInTPM(serialNumber, domain);
}
