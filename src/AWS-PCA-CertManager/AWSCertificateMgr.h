#ifndef AWSCERTIFICATE_MGR
#define AWSCERTIFICATE_MGR

// C++ Include Files
#include <string>

// Include Secure string class
#include "SecureString.h"

// Interface for NGCS TPM Abstraction
#include "NGCSTPM.h"

// AWS ACM-PCA (Private Certificate Authority) API Include Files
#include <aws/acm-pca/ACMPCAClient.h>

#include <openssl/x509.h>
#include <openssl/provider.h>

class AWSCertificateMgr
{

public:
  // CTOR
  AWSCertificateMgr(void);

  enum CertificateValidity
  {
    IsValid,
    IsNOTValid
  };

  /**
   * @brief Checks if the device certificate exists and matches the specified serial number and domain.
   *
   * This function checks if the device certificate exists in the specified directory,
   * and verifies if the certificate's subject matches the provided serial number and domain.
   *
   * @param serialNumber The serial number to be checked against the certificate's subject.
   * @param domain The domain to be checked against the certificate's subject.
   *
   * @return true if the device certificate exists and matches the specified serial number and domain, false otherwise.
   */
  const CertificateValidity CheckDeviceCertificateInTPM(const std::string &serialNumber, const std::string domain);

  /**
   * @brief Generate a device certiifcate (no checks are performed so the cert is always issued)
   *
   * @param certsDirectory The directory where to place the device certificate.
   * @param serialNumber The device serial number
   * @param domain The domain to be used
   *
   * @return true if the device certificate was issued, false otherwise.
   */
  void GenerateDeviceCertificate(const std::string &deviceCSR,
                                 const std::string &certsDirectory,
                                 const std::string &serialNumber,
                                 const std::string domain);

  void encryptAWSAccountIDs(const std::string &accountID, const std::string &sessionToken, const std::string &jsonFilename);

  void printDecryptedAWSAccountIDs(const std::string &encryptedAccountID, const std::string &encryptedSessionToken);

  int writeCertificateToTPM(const std::string &certificate, const unsigned long nvIndexHandle);

  static std::string base64Decode(const std::string &input);
  static std::string base64Encode(const std::string &input);

  ~AWSCertificateMgr(void);

private:
  OSSL_LIB_CTX *tpm2_libctx; // OpenSSL Context

  OSSL_PROVIDER *tpmProvider; // TPM2 OpenSSL Provider
  OSSL_PROVIDER *defaultProv; // default OpenSSL Provider

  /**
   * @brief Generate a private key.
   *
   * This function generates a private key using OpenSSL.
   *
   * @return A pointer to the generated private key.
   */

  EVP_PKEY *generatePrivateKey(void);

  // Function to write private key to file
  void writePrivateKeyToFile(EVP_PKEY *pkey, const std::string &certsDirectory, const std::string &serialNumber);

  /**
   * @brief Generate a private key as a string.
   *
   * This function generates a private key using OpenSSL and returns it as a string.
   *
   * @return The generated private key as a string.
   */
  std::string GeneratePrivateKey(const std::string &certsDirectory, const std::string &serialNumber);

  /**
   * @brief Generate a CSR as a string.
   *
   * This function generates a CSR using OpenSSL and returns it as a string.
   *
   * @return The generated CSR as a string.
   */
  std::string GenerateCSR(const std::string &privateKey, const std::string &serialNumber, const std::string &domain);

  /**
   * Retrieves a certificate bundle, including the specified certificate,
   * intermediate CA certificate, and saves them to a file in the specified directory.
   *
   * This function queries the AWS Certificate Manager Private Certificate Authority (ACM PCA)
   * for a certificate bundle identified by the certificate ARN. It then saves the certificate
   * and intermediate CA certificate into a single file within the specified directory. The
   * operation requires an active ACM PCA client configured with the appropriate permissions
   * and endpoint.
   *
   * @param certificateArn The ARN (Amazon Resource Name) of the certificate to retrieve.
   * @param serialNumber The serial number of the certificate. This may be used for logging or file naming.
   * @param acmPcaClient Reference to the initialized ACM PCA client used to perform certificate operations.
   * @param privateCAEndpoint The endpoint URL of the Private Certificate Authority to be used for requests.
   *
   * @return Returns the certificate or throws
   */
  const std::string RetrieveCertificateFromAWS(const std::string &certificateArn,
                                               const std::string &serialNumber,
                                               Aws::ACMPCA::ACMPCAClient &acmPcaClient,
                                               const Aws::String &privateCAEndpoint);

  /**
   * @brief Issue a new certificate for the device.
   *
   * This function issues a new certificate for the device identified by the provided account ID and session token.
   *
   * @param accountID The AWS account ID of the device.
   * @param sessionToken The AWS session token for authentication.
   * @param intermediateCA The path to the Intermediate CA signing cert is stored (for creating the bundle)
   * @param certsDirectory The directory where the issued certificate will be stored.
   * @param certFilename The filename for the issued certificate.
   * @param serialNumber The serial number to be included in the certificate.
   * @param domain The domain associated with the device.
   *
   * @return Returns 1 if the certificate is issued successfully, -1 if an error occurs.
   */

  const std::string GenerateCSRWithTPM(const std::string &serialNumber, const std::string &domain);

  const std::string IssueNewCertificate(const SecureString &accountID,
                                        const SecureString &sessionToken,
                                        const std::string &serialNumber,
                                        const std::string &domain,
                                        const std::string &deviceCSR);

  static std::vector<unsigned char> hexStringToBytes(const std::string &hex);

  static std::string decrypt(const std::string &ciphertext);
  static std::string encrypt(const std::string &plaintext, const std::string &salt);

  void addSubjectAltNameExtension(X509_REQ *req, const std::string &serialNumber, const std::string &domain);

  X509_REQ *generateCSR(EVP_PKEY *pkey, const std::string &serialNumber, const std::string &domain);

  Aws::Utils::ByteBuffer ReadCSRFromString(const std::string &csrString);

  std::string IssueCertificate(Aws::ACMPCA::ACMPCAClient &acmClient, const Aws::String &privateCAEndpoint, const std::string &deviceCSR);

  std::string GetCertificateAsString(Aws::ACMPCA::ACMPCAClient &acmClient, const Aws::String &privateCAEndpoint, const std::string &certificateArn);

  bool hasCertificateExpired(const std::string &filename);

  std::string getSubjectFromCert(const std::string &certPath);

  bool checkSubjectMatchesDevice(const std::string &certPath, const std::string &serialNumber, const std::string &domain);

  /**
   * @brief Check if an existing certificate matches the device.
   *
   * This function checks if the existing certificate file specified by
   * certificateFilename matches the given serialNumber and domain.
   *
   * @param LSPCertificate The LSP Device certificate
   * @param IntermediateCert The Intermediate CA certificate
   * @param serialNumber The serial number of the device.
   * @param domain The domain of the device.
   * @return True if the existing certificate matches the device, false otherwise.
   */
  bool CheckExistingCertificates(const std::string &LSPCertificate,
                                 const std::string &IntermediateCert,
                                 const std::string &serialNumber,
                                 const std::string &domain);

  void saveEncryptedValuesToJsonFile(const SecureString &encryptedAccountID,
                                     const SecureString &encryptedSessionToken,
                                     const std::string &filename);

  void ReplaceExistingCertificateInTPM(NGCSTPM &NGCSTPMAbstraction, const std::string &serialNumber, const std::string &domain);

  /**
   * @brief Demonstrates accessing a private key stored within a TPM using OpenSSL.
   *
   * This code snippet demonstrates a method to access a private key stored within a TPM using the OpenSSL library,
   * specifically targeting the newer OSSL_STORE API. OpenSSL can perform cryptographic operations with the key
   * securely stored in a TPM, without exposing the key material outside the TPM.
   *
   * @section method_explanation Method Explanation
   *
   * - <b>Opening a Store Context:</b> The function `OSSL_STORE_open_ex` opens a store context for a specified URI,
   *   which in this case is a TPM handle. This handle is a reference to the key's location, not the key itself.
   *   The store context allows OpenSSL to access keys in a uniform manner. The query `"?provider=tpm2"` specifies
   *   using the TPM 2.0 provider.
   *
   * - <b>Reading the Key:</b> The loop uses `OSSL_STORE_load` to load objects from the store context.
   *   It checks if the loaded object is a private key (`OSSL_STORE_INFO_PKEY`).
   *
   * - <b>Accessing the Private Key:</b> If a private key is found, `OSSL_STORE_INFO_get1_PKEY` is used to access it.
   *   This increases the reference count of the `EVP_PKEY` object, indicating that the caller is responsible for its
   *   memory management. The `EVP_PKEY` object can then be used for OpenSSL cryptographic operations,
   *   while the actual key material remains within the TPM.
   *
   * - <b>Closure and Cleanup:</b> The loop exits once the key is found or there are no more objects to load.
   *   Afterward, the store context is closed, and the `EVP_PKEY` object is returned for application use.
   *
   * @section security_and_usage Security and Usage
   *
   * This method ensures the private key material does not leave the TPM, aligning with TPM's security model.
   * It highlights the integration of OpenSSL with TPM hardware for secure cryptographic operations.
   * The application is responsible for managing the `EVP_PKEY` object's memory and securely handling cryptographic operations.
   */
  EVP_PKEY *GetPrivateKeyReferenceFromTPM(void);

  unsigned long parseHexString(const std::string &hexString);
};

#endif // AWSCERTIFICATE_MGR
