/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: March 13th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * LSP: AWS Certificate Mananger Library
 */

// C++ Include Files
#include <string>
#include <vector>
#include <stdexcept>
#include <iostream>
#include <fstream>
#include <cstring>
#include <filesystem>

// OpenSSL Include Files
#include <openssl/rsa.h>
#include <openssl/pem.h>
#include <openssl/x509.h>
#include <openssl/ts.h>
#include <openssl/err.h>

#include <openssl/x509v3.h>
#include <openssl/core_names.h>
#include <openssl/params.h>

#include <openssl/evp.h>
#include <openssl/aes.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <openssl/provider.h>

#include <openssl/store.h>
#include <openssl/ssl.h>

// AWS Core API Include Files
#include <aws/core/Aws.h>
#include <aws/core/auth/AWSCredentialsProvider.h>
#include <aws/core/client/ClientConfiguration.h>
#include <aws/core/auth/AWSCredentialsProviderChain.h>

// AWS ACM-PCA (Private Certificate Authority) API Include Files
#include <aws/acm-pca/ACMPCAClient.h>
#include <aws/acm-pca/model/IssueCertificateRequest.h>
#include <aws/acm-pca/model/GetCertificateRequest.h>
#include <aws/acm-pca/model/GetCertificateResult.h>
#include <aws/acm-pca/model/ValidityPeriodType.h>
#include <aws/acm-pca/ACMPCAServiceClientModel.h>

// Include the LSP Library header
#include "LSPAppLib.hpp"

// Include interface for this class
#include "AWSCertificateMgr.h"

// Include encrypted AWS credentials
#include "AWSConfig.h"

// Include the TPM handle info
#include "TPMConfig.h"

// Interface for NGCS TPM Abstraction
#include "NGCSTPM.h"

// Certificate validity period
const int certValidityMonths = 18;

#define PROD_INTERMEDIATE_CERT_FILE "/etc/ssl/certs/LSP-intermediate-ca.crt"
#define STAGING_INTERMEDIATE_CERT_FILE "/etc/ssl/certs/LSP-staging-intermediate-ca.crt"

// The logger is used by the library code so it is declared and initialized there and extern here
extern log4cxx::LoggerPtr LSPAppLogger;

// Values needed to encrypt and decrypt the AWS credentials
const std::string salt = "F17938B78677FE2B";
const std::string keyHex = "E8CC4BA0F2744A742EBDD0EEACD003F094D85EE4B10F9962AEE7F810D52894EC";
const std::string ivHex = "C8266A140301B4AB274932D1FCB0FFCC";

using namespace std;
using namespace Aws;
using namespace Aws::ACMPCA;
using namespace Aws::ACMPCA::Model;

/**
 * @brief Constructs an AWSCertificateMgr instance.
 *
 * This constructor initializes the AWSCertificateMgr object. During the initialization,
 * it checks for the existence of a TPM (Trusted Platform Module) device by looking for
 * the "/dev/tpmrm0" file. If the TPM device exists, it attempts to load the TPM 2.0 provider
 * using OpenSSL's OSSL_PROVIDER_load function. Success or failure of loading the TPM provider
 * is logged using the LOG4CXX_INFO logging macro. This approach ensures that TPM-based
 * operations are only attempted if the TPM hardware is present and accessible, enhancing
 * the robustness of the application in environments with variable hardware configurations.
 *
 * Note: This constructor assumes that logging via LOG4CXX_INFO and the OpenSSL library
 * are properly configured and available in the application context.
 */

AWSCertificateMgr::AWSCertificateMgr(void)
{

    tpm2_libctx = NULL;
    tpmProvider = NULL;
    defaultProv = NULL;

    // Only try to load the tpm2 provider if the tpm resource manager device exists
    if (LSPApp::FileExists("/dev/tpmrm0"))
    {

        tpm2_libctx = OSSL_LIB_CTX_new();

        tpmProvider = OSSL_PROVIDER_load(tpm2_libctx, "tpm2");
        defaultProv = OSSL_PROVIDER_load(tpm2_libctx, "default");

        if (tpmProvider)
        {
            LOG4CXX_INFO(LSPAppLogger, "Using TPM Provider for Crypto");
        }
    }
}

/**
 * @brief Destroys the AWSCertificateMgr instance.
 *
 * This destructor is responsible for cleaning up resources used by the AWSCertificateMgr object.
 * If the TPM provider was loaded during the construction of this object, it ensures that
 * the provider is properly unloaded to free any resources it has allocated.
 *
 * Note: This implementation assumes that any necessary cleanup operations are either
 * implicitly handled by the destructor or are not required. If future versions of this
 * class allocate dynamic resources or acquire other system resources, this destructor
 * should be updated to explicitly release those resources.
 */

AWSCertificateMgr::~AWSCertificateMgr(void)
{

    if (tpmProvider)
    {
        OSSL_PROVIDER_unload(tpmProvider);
        tpmProvider = NULL;
    }

    if (defaultProv)
    {
        OSSL_PROVIDER_unload(defaultProv);
        defaultProv = NULL;
    }

    if (tpm2_libctx)
    {
        OSSL_LIB_CTX_free(tpm2_libctx);
        tpm2_libctx = NULL;
    }
}

/**
 * @brief Converts a hexadecimal string to a byte array.
 *
 * This utility function takes a hexadecimal string representation and converts it into
 * a vector of bytes (unsigned char). Each pair of hex characters (00 to FF) is converted
 * to its corresponding byte value. The function assumes that the input string is well-formed
 * and contains an even number of hexadecimal digits.
 *
 * @param hex The hexadecimal string to be converted into bytes. It should not contain any
 * non-hex characters and should have an even length.
 *
 * @return std::vector<unsigned char> A vector of bytes representing the binary data of the
 * input hexadecimal string.
 */
std::vector<unsigned char> AWSCertificateMgr::hexStringToBytes(const std::string &hex)
{

    std::vector<unsigned char> bytes;
    for (size_t i = 0; i < hex.length(); i += 2)
    {
        std::string byteString = hex.substr(i, 2);
        unsigned char byte = static_cast<unsigned char>(strtol(byteString.c_str(), nullptr, 16));
        bytes.push_back(byte);
    }
    return bytes;
}

/**
 * @brief Function to decrypt a ciphertext using a salt.
 *
 * This function decrypts encrypted ciphertext using a salt and returns the decrypted result.
 *
 * @param ciphertext The encrypted text to be decrypted.
 * @param salt The salt used in the decryption process.
 * @return The decrypted plaintext.
 */

std::string AWSCertificateMgr::decrypt(const std::string &ciphertext)
{
    auto key = hexStringToBytes(keyHex);
    auto iv = hexStringToBytes(ivHex);

    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    if (!ctx)
        throw std::runtime_error("Failed to create EVP_CIPHER_CTX");

    if (1 != EVP_DecryptInit_ex(ctx, EVP_aes_256_cbc(), nullptr, key.data(), iv.data()))
    {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("EVP_DecryptInit_ex failed");
    }

    std::vector<unsigned char> plaintext(ciphertext.size());
    int len;
    int plaintext_len;

    if (1 != EVP_DecryptUpdate(ctx, plaintext.data(), &len, reinterpret_cast<const unsigned char *>(ciphertext.data()), ciphertext.size()))
    {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("EVP_DecryptUpdate failed");
    }
    plaintext_len = len;

    if (1 != EVP_DecryptFinal_ex(ctx, plaintext.data() + len, &len))
    {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("EVP_DecryptFinal_ex failed. Possible incorrect key/iv or data is corrupted");
    }
    plaintext_len += len;

    EVP_CIPHER_CTX_free(ctx);
    plaintext.resize(plaintext_len);

    return std::string(plaintext.begin(), plaintext.end()); // Assuming raw binary output
}

/**
 * @brief Encrypts plaintext using AES-256-CBC encryption.
 *
 * This method encrypts a given plaintext string using AES-256-CBC encryption algorithm.
 * It utilizes a predefined hexadecimal key (`keyHex`) and initialization vector (`ivHex`),
 * which are converted from hex to bytes. The method ensures secure encryption by leveraging
 * OpenSSL's cryptographic functions. It is designed to handle errors gracefully by throwing
 * exceptions in case of any failure during the encryption process.
 *
 * @param plaintext The std::string containing the text to be encrypted. It should not be empty.
 * @param salt The std::string used to derive the encryption key and IV. In this implementation,
 * the `salt` parameter is not directly used but implies that the actual encryption key and IV
 * might be derived or influenced by a salt elsewhere in the class or application logic.
 *
 * @return std::string A string containing the ciphertext. This output represents the raw binary
 * data of the encrypted content and may contain non-printable characters.
 *
 * @exception std::runtime_error Throws if any step of the encryption process fails, including
 * creating the encryption context, initializing the encryption operation, updating the ciphertext
 * buffer, or finalizing the encryption.
 *
 * @note The encryption key (`keyHex`) and IV (`ivHex`) are assumed to be defined elsewhere in
 * the class and should be securely managed. The use of AES-256-CBC requires careful handling of
 * both the key and IV to ensure security.
 */
std::string AWSCertificateMgr::encrypt(const std::string &plaintext, const std::string &salt)
{
    auto key = hexStringToBytes(keyHex);
    auto iv = hexStringToBytes(ivHex);

    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    if (!ctx)
        throw std::runtime_error("Failed to create EVP_CIPHER_CTX");

    if (1 != EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), nullptr, key.data(), iv.data()))
    {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("EVP_EncryptInit_ex failed");
    }

    std::vector<unsigned char> ciphertext(plaintext.length() + EVP_MAX_BLOCK_LENGTH);
    int len;
    int ciphertext_len;

    if (1 != EVP_EncryptUpdate(ctx, ciphertext.data(), &len, reinterpret_cast<const unsigned char *>(plaintext.data()), plaintext.length()))
    {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("EVP_EncryptUpdate failed");
    }
    ciphertext_len = len;

    if (1 != EVP_EncryptFinal_ex(ctx, ciphertext.data() + len, &len))
    {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("EVP_EncryptFinal_ex failed");
    }
    ciphertext_len += len;

    EVP_CIPHER_CTX_free(ctx);
    ciphertext.resize(ciphertext_len);

    return std::string(ciphertext.begin(), ciphertext.end()); // Assuming raw binary output
}

/**
 * @brief Encodes a given input string into Base64 format.
 *
 * This method encodes any given input string into its Base64 representation using OpenSSL's BIO objects.
 * It configures the BIO chain to ignore newline characters, ensuring the encoded output is generated as
 * a single line string. This functionality is particularly useful for encoding binary data or text into a
 * format that can be easily transmitted or stored in text-based storage systems.
 *
 * @param input The std::string containing the data to be encoded into Base64. The input can be binary
 * data or regular text, and it is treated as a raw byte array without any assumptions on its content
 * or encoding.
 *
 * @return std::string A string representing the Base64 encoded version of the input. The output string
 * does not contain any newline characters, regardless of the input size, adhering to the
 * BIO_FLAGS_BASE64_NO_NL flag setting.
 *
 * @note This method uses OpenSSL's BIO functions for Base64 encoding. The OpenSSL library must be
 * properly initialized in the application, and appropriate error handling should be in place for
 * OpenSSL-related operations. The method ensures to free all allocated BIO structures before returning.
 *
 * @warning The function assumes that the OpenSSL library's BIO functions behave as expected and do not
 * fail under normal conditions. However, error handling mechanisms are not explicitly documented here
 * and should be considered in a production environment, especially for critical applications.
 */

std::string AWSCertificateMgr::base64Encode(const std::string &input)
{
    BIO *bio, *b64;
    BUF_MEM *bufferPtr;

    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL); // Ignore newlines - write everything in one line
    BIO_write(bio, input.c_str(), input.length());
    BIO_flush(bio);
    BIO_get_mem_ptr(bio, &bufferPtr);
    BIO_set_close(bio, BIO_NOCLOSE);
    BIO_free_all(bio);

    return std::string(bufferPtr->data, bufferPtr->length);
}

/**
 * @brief Function to decode a Base64 encoded string.
 *
 * This function decodes a Base64 encoded string and returns the decoded result.
 *
 * @param input The Base64 encoded string to be decoded.
 * @return The decoded string.
 */

std::string AWSCertificateMgr::base64Decode(const std::string &input)
{
    BIO *bio, *b64;
    char buffer[input.size()];
    memset(buffer, 0, sizeof(buffer));

    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new_mem_buf(input.c_str(), input.size());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL); // Ignore newlines
    int length = BIO_read(bio, buffer, input.size());
    BIO_free_all(bio);

    return std::string(buffer, length);
}

/**
 * @brief Function to add the Subject Alt Name extension to a Certificate Signing Request (CSR).
 *
 * This function adds the Subject Alt Name extension to the provided CSR.
 *
 * @param req The X509_REQ object representing the CSR.
 * @param serialNumber The serial number to be included in the Subject Alt Name extension.
 * @param domain The domain name to be included in the Subject Alt Name extension.
 */

void AWSCertificateMgr::addSubjectAltNameExtension(X509_REQ *req, const std::string &serialNumber, const std::string &domain)
{

    // Create a stack of X509_EXTENSION objects
    STACK_OF(X509_EXTENSION) *ext_stack = sk_X509_EXTENSION_new_null();
    if (!ext_stack)
    {
        LOG4CXX_FATAL(LSPAppLogger, "Error creating stack of X509_EXTENSION objects");
        return;
    }

    // Create the subject alternative name extension
    X509_EXTENSION *ext = X509V3_EXT_conf_nid(nullptr, nullptr, NID_subject_alt_name,
                                              (char *)("DNS:" + serialNumber + "." + domain).c_str());
    if (!ext)
    {
        LOG4CXX_FATAL(LSPAppLogger, "Error creating subject alternative name extension");
        sk_X509_EXTENSION_free(ext_stack);
        return;
    }

    // Add the extension to the stack
    if (!sk_X509_EXTENSION_push(ext_stack, ext))
    {
        LOG4CXX_ERROR(LSPAppLogger, "Error adding extension to stack");
        X509_EXTENSION_free(ext);
        sk_X509_EXTENSION_free(ext_stack);
        return;
    }

    // Add the extensions to the certificate request
    if (!X509_REQ_add_extensions(req, ext_stack))
    {
        LOG4CXX_ERROR(LSPAppLogger, "Error adding extensions to certificate request");
        sk_X509_EXTENSION_pop_free(ext_stack, X509_EXTENSION_free);
        return;
    }

    // Free the memory allocated for the stack of extensions
    sk_X509_EXTENSION_pop_free(ext_stack, X509_EXTENSION_free);
}

/**
 * @brief Function to generate a Certificate Signing Request (CSR).
 *
 * This function generates a CSR using the provided private key, serial number, and domain name.
 *
 * @param pkey The EVP_PKEY object representing the private key.
 * @param serialNumber The serial number to be included in the CSR.
 * @param domain The domain name to be included in the CSR.
 * @return A pointer to the generated X509_REQ object representing the CSR.
 */

X509_REQ *AWSCertificateMgr::generateCSR(EVP_PKEY *pkey, const string &serialNumber, const string &domain)
{

    X509_REQ *req = X509_REQ_new();
    X509_NAME *name = X509_REQ_get_subject_name(req);

    // Build the CN string including the serial number and domain
    const string cn = serialNumber + "." + domain;

    // Convert the CN string to the appropriate format
    const char *cnChar = cn.c_str();

    // Add the entries to the subject name
    X509_NAME_add_entry_by_txt(name, "C", MBSTRING_ASC, (const unsigned char *)"US", -1, -1, 0);
    X509_NAME_add_entry_by_txt(name, "ST", MBSTRING_ASC, (const unsigned char *)"Utah", -1, -1, 0);
    X509_NAME_add_entry_by_txt(name, "L", MBSTRING_ASC, (const unsigned char *)"Sandy", -1, -1, 0);
    X509_NAME_add_entry_by_txt(name, "O", MBSTRING_ASC, (const unsigned char *)"Opengear, Inc", -1, -1, 0);
    X509_NAME_add_entry_by_txt(name, "CN", MBSTRING_ASC, (const unsigned char *)cnChar, -1, -1, 0);

    // Add the Subject Alt Name as DNS:serialNumber.domain
    addSubjectAltNameExtension(req, serialNumber, domain);

    X509_REQ_set_pubkey(req, pkey);
    X509_REQ_sign(req, pkey, EVP_sha256());
    return req;
}

/**
 * @brief Function to read a CSR (Certificate Signing Request) from a string.
 *
 * This function takes a CSR in string format and converts it into a byte buffer
 * for further processing.
 *
 * @param csrString The CSR string to read.
 * @return The CSR data as a byte buffer.
 */

Aws::Utils::ByteBuffer AWSCertificateMgr::ReadCSRFromString(const std::string &csrString)
{
    // Create a stringstream from the CSR string
    Aws::StringStream ss;
    ss << csrString;

    // Read the contents of the stringstream into a ByteBuffer
    std::string csrData = ss.str();
    Aws::Utils::ByteBuffer csrBuffer(reinterpret_cast<const unsigned char *>(csrData.c_str()), csrData.length());

    return csrBuffer;
}

/**
 * @brief Function to issue a certificate using ACM PCA (AWS Certificate Manager Private Certificate Authority).
 *
 * This function sends a certificate signing request (CSR) to ACM PCA and retrieves the issued certificate.
 *
 * @param acmClient The ACM PCA client to use for issuing the certificate.
 * @param privateCAEndpoint The endpoint of the private CA.
 * @param deviceCSR The CSR (Certificate Signing Request) string.
 * @return The issued certificate as a string.
 */

std::string AWSCertificateMgr::IssueCertificate(Aws::ACMPCA::ACMPCAClient &acmClient, const Aws::String &privateCAEndpoint, const string &deviceCSR)
{

    IssueCertificateRequest request;
    request.SetSigningAlgorithm(SigningAlgorithm::SHA256WITHRSA);
    request.SetCertificateAuthorityArn(privateCAEndpoint);

    // Create a JSON object representing the validity period
    Aws::Utils::Json::JsonValue validityJson;
    validityJson.WithInteger("Value", certValidityMonths); // Set the value to n Months
    validityJson.WithString("Type", "MONTHS");

    LOG4CXX_INFO(LSPAppLogger, "Issuing certificate valid for " << certValidityMonths << " months");

    // Create a Validity object using the JSON representation
    Aws::ACMPCA::Model::Validity validity(validityJson);
    request.SetValidity(validity);

    Aws::Utils::ByteBuffer csr = ReadCSRFromString(deviceCSR);
    request.SetCsr(csr);

    IssueCertificateOutcome outcome = acmClient.IssueCertificate(request);
    if (!outcome.IsSuccess())
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to issue certificate: " << outcome.GetError().GetMessage());

        return string("");
    }

    LOG4CXX_INFO(LSPAppLogger, "Certificate Issued successfully");

    return outcome.GetResult().GetCertificateArn();
}

/**
 * @brief Function to retrieve a certificate from ACM PCA (AWS Certificate Manager Private Certificate Authority).
 *
 * This function retrieves a certificate from ACM PCA using the provided client and endpoint information.
 *
 * @param acmClient The ACM PCA client used to retrieve the certificate.
 * @param privateCAEndpoint The endpoint of the private CA.
 * @param certificateArn The ARN (Amazon Resource Name) of the certificate.
 * @return The retrieved certificate as a string.
 */

std::string AWSCertificateMgr::GetCertificateAsString(Aws::ACMPCA::ACMPCAClient &acmClient, const Aws::String &privateCAEndpoint, const std::string &certificateArn)
{

    Aws::ACMPCA::Model::GetCertificateRequest request;

    // Define the AWS Private CA Endpoint and the ARN for the issued certificate
    request.SetCertificateAuthorityArn(privateCAEndpoint);
    request.SetCertificateArn(certificateArn);

    const int waitTimeInSeconds = 10;
    // Loop for up to 10 seconds waiting for AWS to issue the certificate
    for (int i = 0; i < waitTimeInSeconds; i++)
    {
        Aws::ACMPCA::Model::GetCertificateOutcome outcome = acmClient.GetCertificate(request);

        if (outcome.IsSuccess())
        {
            // Certificate received so return it as a string
            return outcome.GetResult().GetCertificate();
        }
        else
        {
            // Check if the error is due to certificate still being processed
            auto error = outcome.GetError();
            if (error.GetErrorType() == Aws::ACMPCA::ACMPCAErrors::REQUEST_IN_PROGRESS)
            {
                // Wait for 1 second before retrying
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
            else
            {
                // Throw an exception with the error message
                throw std::runtime_error("Failed to get certificate: " + error.GetMessage());
            }
        }
    }

    // If execution reaches here, certificate retrieval failed after several attempts
    throw std::runtime_error("Failed to get certificate: Timeout after " + std::to_string(waitTimeInSeconds) + " seconds");
}

/**
 * @brief Check if a certificate file is expired.
 *
 * This function checks if the certificate file specified by the filename
 * is expired.
 *
 * @param certificateData The certificate to check.
 * @return True if the certificate is expired, false otherwise.
 */

bool AWSCertificateMgr::hasCertificateExpired(const std::string &certificateData)
{

    // Create a BIO memory buffer with the string data
    BIO *bio = BIO_new_mem_buf(certificateData.data(), certificateData.length());

    // Use PEM_read_bio_X509 to read the X509 certificate from the BIO
    X509 *certificate = PEM_read_bio_X509(bio, NULL, NULL, NULL);

    // Remember to free the BIO when done
    BIO_free(bio);

    if (!certificate)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to read certificate from string.");
        return true;
    }

    // Get certificate expiration date
    ASN1_TIME *notAfter = X509_get_notAfter(certificate);
    if (!notAfter)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to get certificate expiration date.");
        X509_free(certificate);
        return true;
    }

    // Convert ASN1_TIME to time_t manually
    time_t notAfterTime = 0;
    const char *timeStr = (const char *)notAfter->data;
    std::string timeStrCopy(timeStr, timeStr + notAfter->length);

    struct tm tm;
    memset(&tm, 0, sizeof(struct tm));
    strptime(timeStrCopy.c_str(), "%y%m%d%H%M%S%z", &tm);
    notAfterTime = mktime(&tm);

    // Get current time
    time_t currentTime = time(NULL);

    // Compare expiration time with current time
    bool expired = difftime(notAfterTime, currentTime) < 0;

    // Free certificate memory
    X509_free(certificate);

    return expired;
}

/**
 * @brief Extract the subject from a certificate file.
 *
 * This function reads the certificate file specified by certPath
 * and extracts the subject information from it.
 *
 * @param certPath The path to the certificate file.
 * @return The subject information extracted from the certificate.
 */

std::string AWSCertificateMgr::getSubjectFromCert(const std::string &certificateData)
{

    // Create a BIO memory buffer with the string data
    BIO *bio = BIO_new_mem_buf(certificateData.data(), certificateData.length());

    // Use PEM_read_bio_X509 to read the X509 certificate from the BIO
    X509 *certificate = PEM_read_bio_X509(bio, NULL, NULL, NULL);

    // Remember to free the BIO when done
    BIO_free(bio);

    if (!certificate)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to read certificate from string.");
        throw std::runtime_error("Failed to read certificate from string");
    }

    char *subject = X509_NAME_oneline(X509_get_subject_name(certificate), NULL, 0);
    std::string subjectStr(subject);
    OPENSSL_free(subject);

    X509_free(certificate);

    return subjectStr;
}

/**
 * @brief Check if the subject of a certificate matches the device.
 *
 * This function checks if the subject information extracted from
 * the certificate file specified by certPath matches the given
 * serialNumber and domain.
 *
 * @param certificateData The certificate data.
 * @param serialNumber The serial number of the device.
 * @param domain The domain of the device.
 * @return True if the subject matches the device, false otherwise.
 */

bool AWSCertificateMgr::checkSubjectMatchesDevice(const std::string &certificateData, const std::string &serialNumber, const std::string &domain)
{

    std::string subject = getSubjectFromCert(certificateData);

    if (subject.empty())
    {
        LOG4CXX_INFO(LSPAppLogger, "Failed to get subject from certificate");
        return false;
    }

    string expected_CN;
    // Map lsppoc to the lsp-staging CA certificate CN
    if (domain.find("lsppoc.opengear.com") != string::npos)
    {
        expected_CN = serialNumber + "." + "lsp-staging.opengear.com";
    }
    else
    {
        expected_CN = serialNumber + "." + domain;
    }

    if (subject.find(expected_CN) != std::string::npos)
    {
        LOG4CXX_INFO(LSPAppLogger, "Certificate matches device serial number");
        return true;
    }

    LOG4CXX_INFO(LSPAppLogger, "Expected CN: " << expected_CN);
    LOG4CXX_INFO(LSPAppLogger, "Subject" << subject);

    LOG4CXX_INFO(LSPAppLogger, "Certificate DOES NOT match device serial number");

    return false;
}

/**
 * @brief Check if an existing certificate matches the device.
 *
 * This function checks if the existing certificates passed in have not expired and
 * whether the serial number in the certificate matches the device serial number.
 *
 * @param certificateData The existing certificate.
 * @param serialNumber The serial number of the device.
 * @param domain The domain of the device.
 * @return True if the existing certificate matches the device, false otherwise.
 */

bool AWSCertificateMgr::CheckExistingCertificates(const std::string &LSPCertificate,
                                                  const std::string &IntermediateCert,
                                                  const std::string &serialNumber,
                                                  const std::string &domain)
{

    LOG4CXX_INFO(LSPAppLogger, "Checking LSP certificate for device serial number: " << serialNumber << " domain: " << domain);

    if (hasCertificateExpired(LSPCertificate))
    {
        LOG4CXX_INFO(LSPAppLogger, "LSP Device Certificate has expired");
        return false;
    }

    if (hasCertificateExpired(IntermediateCert))
    {
        LOG4CXX_INFO(LSPAppLogger, "Intermediate CA Certificate has expired");
        return false;
    }

    LOG4CXX_INFO(LSPAppLogger, "Certificate has not expired");

    return (checkSubjectMatchesDevice(LSPCertificate, serialNumber, domain));
}

/**
 * @brief Generate a private key.
 *
 * This function generates a private key using OpenSSL.
 *
 * @return A pointer to the generated private key.
 */
EVP_PKEY *AWSCertificateMgr::generatePrivateKey(void)
{

    EVP_PKEY *pkey = nullptr;
    EVP_PKEY_CTX *pkey_ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, nullptr);
    if (!pkey_ctx)
    {
        // Error handling
        return nullptr;
    }

    if (EVP_PKEY_keygen_init(pkey_ctx) <= 0)
    {
        // Error handling
        EVP_PKEY_CTX_free(pkey_ctx);
        return nullptr;
    }

    // Setting the RSA key size
    if (EVP_PKEY_CTX_set_rsa_keygen_bits(pkey_ctx, 2048) <= 0)
    {
        // Error handling
        EVP_PKEY_CTX_free(pkey_ctx);
        return nullptr;
    }

    // Generate the key
    if (EVP_PKEY_keygen(pkey_ctx, &pkey) <= 0)
    {
        // Error handling
        EVP_PKEY_CTX_free(pkey_ctx);
        return nullptr;
    }

    EVP_PKEY_CTX_free(pkey_ctx);
    return pkey;
}

// Function to write private key to file
// DEPRECATED
void AWSCertificateMgr::writePrivateKeyToFile(EVP_PKEY *pkey, const std::string &certsDirectory, const string &serialNumber)
{

    FILE *fp = fopen((certsDirectory + "/" + serialNumber + ".key").c_str(), "wb");

    PEM_write_PrivateKey(fp, pkey, nullptr, nullptr, 0, nullptr, nullptr);
    fclose(fp);
}

/**
 * @brief Generate a private key as a string.
 * DEPRECATED
 *
 * This function generates a private key using OpenSSL and returns it as a string.
 *
 * @return The generated private key as a string.
 */

std::string AWSCertificateMgr::GeneratePrivateKey(const std::string &certsDirectory, const std::string &serialNumber)
{

    EVP_PKEY *pkey = generatePrivateKey();

    if (pkey == nullptr)
    {
        throw std::runtime_error("Cannot generate private key");
    }

    // Save the private key to a file
    writePrivateKeyToFile(pkey, certsDirectory, serialNumber);

    // Serialize private key to a memory BIO
    BIO *bio = BIO_new(BIO_s_mem());
    if (bio == nullptr)
    {
        EVP_PKEY_free(pkey);
        throw std::runtime_error("Failed to create memory BIO");
    }

    if (PEM_write_bio_PrivateKey(bio, pkey, nullptr, nullptr, 0, nullptr, nullptr) != 1)
    {
        BIO_free(bio);
        EVP_PKEY_free(pkey);
        throw std::runtime_error("Failed to write private key to memory BIO");
    }

    // Extract the contents of the memory BIO into a string
    char *buffer;
    size_t length = BIO_get_mem_data(bio, &buffer);
    std::string privateKeyString(buffer, length);

    // Clean up
    BIO_free(bio);
    EVP_PKEY_free(pkey);

    // Return the private key string
    return privateKeyString;
}

/**
 * @brief Generate a CSR as a string.
 *
 * This function generates a CSR using OpenSSL and returns it as a string.
 *
 * @return The generated CSR as a string.
 */

std::string AWSCertificateMgr::GenerateCSR(const std::string &privateKey, const std::string &serialNumber, const std::string &domain)
{

    // Deserialize private key from string
    BIO *bioPrivateKey = BIO_new_mem_buf(privateKey.data(), privateKey.size());
    if (!bioPrivateKey)
    {
        throw std::runtime_error("Failed to create memory BIO for private key");
    }

    EVP_PKEY *pkey = PEM_read_bio_PrivateKey(bioPrivateKey, nullptr, nullptr, nullptr);
    if (!pkey)
    {
        BIO_free(bioPrivateKey);
        throw std::runtime_error("Failed to deserialize private key from string");
    }
    BIO_free(bioPrivateKey);

    // Generate CSR
    X509_REQ *req = generateCSR(pkey, serialNumber, domain);
    if (!req)
    {
        EVP_PKEY_free(pkey);
        throw std::runtime_error("Failed to generate CSR");
    }

    // Serialize CSR
    BIO *bioCSR = BIO_new(BIO_s_mem());
    if (!bioCSR)
    {
        X509_REQ_free(req);
        EVP_PKEY_free(pkey);
        throw std::runtime_error("Failed to create memory BIO for CSR");
    }
    PEM_write_bio_X509_REQ(bioCSR, req);

    char *csrBuffer;
    size_t csrLength = BIO_get_mem_data(bioCSR, &csrBuffer);

    // Convert CSR to a string
    std::string csrString(csrBuffer, csrLength);

    // Clean up
    BIO_free(bioCSR);
    X509_REQ_free(req);
    EVP_PKEY_free(pkey);

    return csrString;
}

/**
 * @file Example.cpp
 * @brief Demonstrates accessing a private key stored within a TPM using OpenSSL.
 *
 * This code snippet demonstrates a method to access a private key stored within a TPM using the OpenSSL library,
 * specifically targeting the newer OSSL_STORE API. OpenSSL can perform cryptographic operations with the key
 * securely stored in a TPM, without exposing the key material outside the TPM.
 *
 * @section method_explanation Method Explanation
 *
 * - <b>Opening a Store Context:</b> The function `OSSL_STORE_open_ex` opens a store context for a specified URI,
 *   which in this case is a TPM handle. This handle is a reference to the key's location, not the key itself.
 *   The store context allows OpenSSL to access keys in a uniform manner. The query `"?provider=tpm2"` specifies
 *   using the TPM 2.0 provider.
 *
 * - <b>Reading the Key:</b> The loop uses `OSSL_STORE_load` to load objects from the store context.
 *   It checks if the loaded object is a private key (`OSSL_STORE_INFO_PKEY`).
 *
 * - <b>Accessing the Private Key:</b> If a private key is found, `OSSL_STORE_INFO_get1_PKEY` is used to access it.
 *   This increases the reference count of the `EVP_PKEY` object, indicating that the caller is responsible for its
 *   memory management. The `EVP_PKEY` object can then be used for OpenSSL cryptographic operations,
 *   while the actual key material remains within the TPM.
 *
 * - <b>Closure and Cleanup:</b> The loop exits once the key is found or there are no more objects to load.
 *   Afterward, the store context is closed, and the `EVP_PKEY` object is returned for application use.
 *
 * @section security_and_usage Security and Usage
 *
 * This method ensures the private key material does not leave the TPM, aligning with TPM's security model.
 * It highlights the integration of OpenSSL with TPM hardware for secure cryptographic operations.
 * The application is responsible for managing the `EVP_PKEY` object's memory and securely handling cryptographic operations.
 */

EVP_PKEY *AWSCertificateMgr::GetPrivateKeyReferenceFromTPM(void)
{

    LOG4CXX_INFO(LSPAppLogger, "Called GetPrivateKeyReferenceFromTPM");

    const std::string TPMHandle = "handle:" + std::string(LSP_TPM_Private_Key_Handle);

    OSSL_STORE_CTX *storeCtx = OSSL_STORE_open_ex(TPMHandle.c_str(), tpm2_libctx, "?provider=tpm2",
                                                  NULL, NULL, NULL, NULL, NULL);

    if (!storeCtx)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to open store context");
        return nullptr;
    }

    EVP_PKEY *TPMpkey = nullptr;
    OSSL_STORE_INFO *info = nullptr;

    while (!OSSL_STORE_eof(storeCtx) && (info = OSSL_STORE_load(storeCtx)) != nullptr)
    {
        int type = OSSL_STORE_INFO_get_type(info);

        if (type == OSSL_STORE_INFO_PKEY)
        {
            TPMpkey = OSSL_STORE_INFO_get1_PKEY(info);
            OSSL_STORE_INFO_free(info);

            if (TPMpkey)
            {
                LOG4CXX_INFO(LSPAppLogger, "GetPrivateKeyReferenceFromTPM Succeeded");
                break; // Exit the loop since the key has been found
            }
        }
        else
        {
            OSSL_STORE_INFO_free(info); // Ensure info is freed if not the expected type
        }
    }

    OSSL_STORE_close(storeCtx);

    if (!TPMpkey)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Failed to retrieve TPM key");
    }

    return TPMpkey;
}

/**
 * @brief Generate a CSR as a string. Uses the TPM-resident private key
 *
 * This function generates a CSR using OpenSSL and returns it as a string.
 *
 * @return The generated CSR as a string.
 */

const std::string AWSCertificateMgr::GenerateCSRWithTPM(const std::string &serialNumber, const std::string &domain)
{

    // Get a reference to the Private Key from the TPM, to use for crypto operations. The Private Key
    // stays in the TPM and the Private Key reference is used to sign the CSR using OpenSSL.

    EVP_PKEY *TPMpKeyRef = GetPrivateKeyReferenceFromTPM();
    if (!TPMpKeyRef)
    {
        throw std::runtime_error("Failed to get private key reference from TPM");
    }

    // Generate CSR
    X509_REQ *req = generateCSR(TPMpKeyRef, serialNumber, domain);

    // We are finished with the private key
    if (TPMpKeyRef)
        EVP_PKEY_free(TPMpKeyRef);

    if (!req)
    {
        throw std::runtime_error("Failed to generate CSR");
    }

    // Serialize CSR
    BIO *bioCSR = BIO_new(BIO_s_mem());
    if (!bioCSR)
    {
        X509_REQ_free(req);

        throw std::runtime_error("Failed to create memory BIO for CSR");
    }

    PEM_write_bio_X509_REQ(bioCSR, req);

    char *csrBuffer;
    size_t csrLength = BIO_get_mem_data(bioCSR, &csrBuffer);

    // Convert CSR to a string
    std::string csrString(csrBuffer, csrLength);

    // Clean up
    if (bioCSR)
        BIO_free(bioCSR);
    if (req)
        X509_REQ_free(req);

    return csrString;
}

/**
 * Retrieves a certificate from AWS
 * intermediate CA certificate, and saves them to a file in the specified directory.
 *
 * This function queries the AWS Certificate Manager Private Certificate Authority (ACM PCA)
 * for a certificate bundle identified by the certificate ARN. It then saves the certificate
 * and intermediate CA certificate into a single file within the specified directory. The
 * operation requires an active ACM PCA client configured with the appropriate permissions
 * and endpoint.
 *
 * @param certificateArn The ARN (Amazon Resource Name) of the certificate to retrieve.
 * @param serialNumber The serial number of the certificate. This may be used for logging or file naming.
 * @param acmPcaClient Reference to the initialized ACM PCA client used to perform certificate operations.
 * @param privateCAEndpoint The endpoint URL of the Private Certificate Authority to be used for requests.
 *
 * @return Returns true if the certificate bundle was successfully retrieved and saved, false otherwise.
 */
const std::string AWSCertificateMgr::RetrieveCertificateFromAWS(const std::string &certificateArn,
                                                                const std::string &serialNumber,
                                                                Aws::ACMPCA::ACMPCAClient &acmPcaClient,
                                                                const Aws::String &privateCAEndpoint)
{
    if (!certificateArn.empty())
    {
        // Get the certificate
        const std::string deviceCertificate = GetCertificateAsString(acmPcaClient, privateCAEndpoint, certificateArn);

        LOG4CXX_INFO(LSPAppLogger, "Certificate issued for serial number: " << serialNumber);

        return deviceCertificate;
    }

    throw std::runtime_error("OpenSSL TPM provider is not available. Unable to generate CSR");
}

const Aws::String GetCAEndpointForDomain(const std::string &domain)
{

    if (domain.find("lsppoc.") != std::string::npos)
    {
        return StagingCAArn;
    }

    return ProductionCAArn;
}

/**
 * @brief Issue a new certificate for the device and store it in the TPM
 *
 * This function issues a new certificate for the device identified by the provided account ID and session token.
 *
 * @param accountID The AWS account ID of the device.
 * @param sessionToken The AWS session token for authentication.
 * @param serialNumber The serial number to be included in the certificate.
 * @param domain The domain associated with the device.
 *
 * @return Returns 1 if the certificate is issued successfully, -1 if an error occurs.
 */

const std::string AWSCertificateMgr::IssueNewCertificate(const SecureString &accountID,
                                                         const SecureString &sessionToken,
                                                         const std::string &serialNumber,
                                                         const std::string &domain,
                                                         const std::string &deviceCSR)
{

    LOG4CXX_INFO(LSPAppLogger, "Generating certificate for serial number: " << serialNumber << "." << domain);

    // Initialize the AWS SDK
    Aws::SDKOptions options;
    InitAPI(options);

    // Look up the CA ARN based on the domain (production or staging)
    const Aws::String privateCAEndpoint = GetCAEndpointForDomain(domain);

    // Set the AWS credentials provider - unavoidable because that is the AWS API
    Aws::Auth::AWSCredentials credentials(accountID.toString(), sessionToken.toString());

    // Configure the ACM PCA client
    Aws::Client::ClientConfiguration clientConfig;

    // Create an ACM PCA client with the provided credentials and configuration
    Aws::ACMPCA::ACMPCAClient acmPcaClient(credentials, clientConfig);

    // Issue certificate
    const std::string certificateArn = IssueCertificate(acmPcaClient, privateCAEndpoint, deviceCSR);

    // Get the certificate from the AWS private CA
    const std::string deviceCertificate = RetrieveCertificateFromAWS(certificateArn,
                                                                     serialNumber,
                                                                     acmPcaClient,
                                                                     privateCAEndpoint);
    ShutdownAPI(options);

    return deviceCertificate;
}

/**
 * @brief Checks if the device certificate exists and matches the specified serial number and domain.
 *
 * This function checks if the device certificate exists in the specified directory,
 * and verifies if the certificate's subject matches the provided serial number and domain.
 *
 * @param serialNumber The serial number to be checked against the certificate's subject.
 * @param domain The domain to be checked against the certificate's subject.
 * @return int Returns true if the current certificate exists, has not expired, and can be used
 *             Returns false if there was an error during the operation.
 */

const AWSCertificateMgr::CertificateValidity AWSCertificateMgr::CheckDeviceCertificateInTPM(const std::string &serialNumber, const std::string domain)
{
    try
    {
        NGCSTPM NGCSTPMAbstraction;

        // Read the Intermediate CA Cert from the file system
        std::string IntermedicateCACertData;

        if (domain.find("lsppoc.") != std::string::npos)
        {
            // LOG4CXX_INFO(LSPAppLogger, "reading " << STAGING_INTERMEDIATE_CERT_FILE);

            IntermedicateCACertData = LSPApp::ReadStringFromFile(STAGING_INTERMEDIATE_CERT_FILE);
        }
        else if (domain.find("lsp.") != std::string::npos)
        {
            // LOG4CXX_INFO(LSPAppLogger, "reading " << PROD_INTERMEDIATE_CERT_FILE);

            IntermedicateCACertData = LSPApp::ReadStringFromFile(PROD_INTERMEDIATE_CERT_FILE);
        }

        if (IntermedicateCACertData.empty())
        {
            LOG4CXX_INFO(LSPAppLogger, "Failed to read Intermediate CA Certificate. Domain: " << domain);
            return CertificateValidity::IsNOTValid;
        }

        // Read the LSP device Cert from the TPM
        std::string LSPCertificateData =
            NGCSTPMAbstraction.GetLSPCertificateFromTPM(LSPApp::parseHexString(LSP_TPM_Certificate_Handle));

        if (LSPCertificateData.empty())
        {
            LOG4CXX_INFO(LSPAppLogger, "Failed to read LSP Certificate");
            return CertificateValidity::IsNOTValid;
        }

        const std::string IntermediateCertificate(LSPCertificateData.begin(), LSPCertificateData.end());
        const std::string LSPCertificate(LSPCertificateData.begin(), LSPCertificateData.end());

        // Check whether the certificates have expired and ensure they matches the device serial number
        if (CheckExistingCertificates(LSPCertificate, IntermediateCertificate, serialNumber, domain) == true)
        {
            // Existing LSP certificate in the TPM is valid and can be used
            return CertificateValidity::IsValid;
        }

        // The LSP certificate in the TPM is invalid and can't be used. Generate a new certificate and store it in the TPM
        ReplaceExistingCertificateInTPM(NGCSTPMAbstraction, serialNumber, domain);

        return CertificateValidity::IsValid;
    }
    catch (const std::exception &exc)
    {
        // catch anything thrown within try block that derives from std::exception
        LOG4CXX_FATAL(LSPAppLogger, exc.what());
    }
    return CertificateValidity::IsNOTValid;
}

void AWSCertificateMgr::ReplaceExistingCertificateInTPM(NGCSTPM &NGCSTPMAbstraction, const std::string &serialNumber, const std::string &domain)
{

    if (tpmProvider == NULL)
    {
        throw std::runtime_error("OpenSSL TPM provider is not available. Unable to generate CSR");
    }

    // Generate a CSR for the device
    std::string deviceCSR = GenerateCSRWithTPM(serialNumber, domain);

    // Decrypt the AWS credentials - SecureString zeros the memory on delete
    SecureString decryptedAccountID(decrypt(base64Decode(AWSConfig::ENCRYPTED_ACCOUNT_ID)));
    SecureString decryptedSessionToken(decrypt(base64Decode(AWSConfig::ENCRYPTED_SESSION_TOKEN)));

    // Get the new certificate from AWS
    const std::string deviceCertificate = IssueNewCertificate(decryptedAccountID, decryptedSessionToken, serialNumber, domain, deviceCSR);

    // In this case, the Intermediate Certificate is already stored in the TPM so we only save the new Device Certificate
    NGCSTPMAbstraction.writeCertificateToTPM(deviceCertificate, LSPApp::parseHexString(LSP_TPM_Certificate_Handle));
}

/**
 * @brief Called from the Manufacturing Certificate App to generate a new
 * certificate and store it on the file system
 *
 **/

void AWSCertificateMgr::GenerateDeviceCertificate(const std::string &deviceCSR,
                                                  const std::string &certsDirectory,
                                                  const std::string &serialNumber,
                                                  const std::string domain)
{
    // Decrypt the AWS credentials - SecureString zeros the memory on delete
    SecureString decryptedAccountID(decrypt(base64Decode(AWSConfig::ENCRYPTED_ACCOUNT_ID)));
    SecureString decryptedSessionToken(decrypt(base64Decode(AWSConfig::ENCRYPTED_SESSION_TOKEN)));

    // Get the new certificate from AWS
    const std::string deviceCertificate = IssueNewCertificate(decryptedAccountID,
                                                              decryptedSessionToken,
                                                              serialNumber,
                                                              domain,
                                                              deviceCSR);

    // Save the new certificate to a file (PEM format)
    LSPApp::WriteStringToFile(deviceCertificate, certsDirectory + "/" + serialNumber + ".crt", "Device Certificate");
}

/**
 * @brief Saves encrypted values to a JSON file.
 *
 * This function creates a JSON object and populates it with the encrypted AWS account ID and
 * session token. It then attempts to open the specified file for writing. If successful, it writes
 * the JSON data to the file with formatted indentation for readability. If the file cannot be opened,
 * an runtime exception is thrown.
 *
 * @param encryptedAccountID A SecureString containing the encrypted AWS account ID. The
 * content of this parameter is converted to a string and stored in the JSON object under the
 * key "encryptedAccountID".
 *
 * @param encryptedSessionToken A SecureString containing the encrypted AWS session token.
 * Similar to encryptedAccountID, it is converted to a string and added to the JSON object under
 * the key "encryptedSessionToken".
 *
 * @param filename The name of the file where the JSON data should be saved. The function attempts
 * to open this file for writing. If the file exists, its contents will be overwritten; if it does not exist,
 * a new file will be created.
 *
 * @note This function does not perform encryption by itself; it assumes that the input values are
 * already encrypted. It is the caller's responsibility to ensure that the provided strings are
 * appropriately secured before calling this function.
 *
 * @warning If the file specified by the filename cannot be opened for writing, the function will
 * throw a runtime exception. It is the caller's responsibility to handle such situations appropriately.
 */

void AWSCertificateMgr::saveEncryptedValuesToJsonFile(const SecureString &encryptedAccountID,
                                                      const SecureString &encryptedSessionToken,
                                                      const std::string &filename)
{

    // Create a JSON object
    json jsonData;

    // Add encryptedAccountID and encryptedSessionToken to the JSON object
    jsonData["encryptedAccountID"] = encryptedAccountID.toString();
    jsonData["encryptedSessionToken"] = encryptedSessionToken.toString();

    // Open a file for writing
    std::ofstream outputFile(filename);

    // Check if the file is opened successfully
    if (outputFile.is_open())
    {
        // Write JSON data to the file
        outputFile << std::setw(4) << jsonData << std::endl;
        // Close the file
        outputFile.close();
    }
    else
    {
        // Throw if the file cannot be opened
        throw std::runtime_error("Error: Unable to open file " + filename + " for writing.");
    }
}

/**
 * @brief Encrypts AWS account identifiers and saves them to a JSON file.
 *
 * This method takes an AWS account ID and session token, encrypts them using a predefined
 * salt and encryption method, then encodes the encrypted values in base64. The encrypted and
 * encoded account ID and session token are then saved to a specified JSON file. The method
 * also outputs the encrypted strings to the standard output as a debugging aid.
 *
 * @param accountID The AWS account ID to be encrypted. This is a plaintext string that
 * represents the AWS account identifier.
 * @param sessionToken The AWS session token to be encrypted. This is a plaintext string
 * that provides session authentication.
 *
 * @param jsonFilename The name of the JSON file where the encrypted and encoded values
 * will be saved. The method will overwrite the file if it exists or create a new file if it
 * does not exist.
 *
 * @note The encryption is performed using a predefined salt, which should be securely
 * managed and consistent across encryption operations to ensure that the encryption
 * process is reversible.
 *
 * @warning The method outputs the encrypted and encoded account ID and session token to
 * the standard output. This is intended for debugging purposes only and should be removed
 * or secured in production environments to avoid leaking sensitive information.
 */

void AWSCertificateMgr::encryptAWSAccountIDs(const std::string &accountID,
                                             const std::string &sessionToken,
                                             const std::string &jsonFilename)
{

    SecureString encryptedAccountID(base64Encode(encrypt(accountID, salt)));
    SecureString encryptedSessionToken(base64Encode(encrypt(sessionToken, salt)));

    std::cout << encryptedAccountID.toString() << " " << encryptedSessionToken.toString() << std::endl;

    saveEncryptedValuesToJsonFile(encryptedAccountID, encryptedSessionToken, jsonFilename);
}

/**
 * @brief Decrypts and prints AWS account identifiers.
 *
 * This method takes encrypted and base64-encoded AWS account ID and session token strings,
 * decodes and decrypts them, and then prints the decrypted values to the standard output.
 * It utilizes the SecureString class for handling the decrypted values, ensuring that the
 * memory used to store these values is securely zeroed when no longer needed to minimize
 * the risk of sensitive information being left in memory.
 *
 * @param encryptedAccountID A base64-encoded and encrypted string representing the AWS
 * account ID. This string will be base64-decoded and then decrypted to recover the original
 * plaintext account ID.
 *
 * @param encryptedSessionToken A base64-encoded and encrypted string representing the AWS
 * session token. This string will undergo the same process as the account ID to recover the
 * plaintext session token.
 *
 * @note This method is intended for debugging or logging purposes where it is necessary to
 * verify the correct decryption of encrypted AWS credentials. Use caution when displaying
 * decrypted sensitive information to ensure it is not exposed to unauthorized parties.
 *
 * @warning Since this method prints decrypted sensitive information to the standard output,
 * it should be used judiciously and with awareness of the security implications. Avoid using
 * it in production environments or in any context where the output could be accessed by
 * unauthorized individuals.
 */

void AWSCertificateMgr::printDecryptedAWSAccountIDs(const std::string &encryptedAccountID, const std::string &encryptedSessionToken)
{

    // Decrypt the AWS credentials - SecureString zeros the memory on delete
    SecureString decryptedAccountID(decrypt(base64Decode(encryptedAccountID)));
    SecureString decryptedSessionToken(decrypt(base64Decode(encryptedSessionToken)));

    std::cout << "Decrypted " << decryptedAccountID.toString() << std::endl;
    std::cout << "Decrypted " << decryptedSessionToken.toString() << std::endl;
}
