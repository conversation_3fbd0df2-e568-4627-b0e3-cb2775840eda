/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: February 14th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal NGCS Application
 */

// C++ Include Files
#include <iostream>
#include <fstream>
#include <sstream>
#include <string>

#include <openssl/evp.h>
#include <openssl/aes.h>

// JSON for C++
#include "nlohmann/json.hpp"

// Include the LSP Library header
#include "LSPAppLib.hpp"

// AWS Certificate Manager wrapper
#include "AWSCertificateMgr.h"

// And json for Modern c++
using json = nlohmann::json;

const std::string LSPEnrollmentFilePath = "/opt/LSP/results/lh_enrollment.json";

const std::string JSONEncryptionKey = "efkhaM0gCOb6tLo5Ww94mZsiMz15duHxEh8na5p9J6E=";

// The logger is used by the library code so it is declared there and initialized there
log4cxx::LoggerPtr LSPAppLogger = LSPApp::getLogger("LSP Enrollment");

/**
 * @brief Sends a message to the LSPBootstrap application via the enrollment FIFO.
 *
 * This function attempts to open a predefined FIFO and writes an enrollment message to it. If the FIFO
 * cannot be opened, it throws an exception. This method is typically used to communicate enrollment data
 * or commands to another process.
 *
 * @param enrollmentMessage The message to be written to the FIFO.
 * @throws std::runtime_error If the FIFO file cannot be opened, indicating a failure in the communication setup.
 *
 * @note The path to the FIFO is defined by LSP_ENROLLMENT_FIFO. This function assumes that the FIFO exists
 *       and is accessible.
 */
void SendEnrollmentFifo(const std::string &enrollmentMessage)
{

    try
    {
        std::ofstream fifo(LSP_ENROLLMENT_FIFO);

        if (!fifo.is_open())
        {
            throw std::runtime_error("Failed to open FIFO: " LSP_ENROLLMENT_FIFO);
        }

        fifo << enrollmentMessage;

        if (fifo.fail())
        {
            throw std::runtime_error("Failed to write to FIFO: " LSP_ENROLLMENT_FIFO);
        }
        else
            LOG4CXX_INFO(LSPAppLogger, "Wrote " << enrollmentMessage << " to FIFO");

        // The destructor of std::ofstream will automatically close the FIFO
    }
    catch (const std::exception &e)
    {
        // Handle or log the exception as needed
        throw;
    }
}

/**
 * @brief Displays the usage instructions for the application.
 *
 * This function prints the command-line usage instructions to the standard error stream. It provides
 * details on various options available for running the program. The usage information includes flags
 * for verbose output, setting a password, specifying an account, and displaying help.
 *
 * @param name The name of the program, typically extracted from argv[0].
 *
 * @note This function is intended to be called when the user passes incorrect command-line arguments
 *       or explicitly requests help via the '-h' or '--help' options.
 */
static void show_usage(std::string name)
{
    std::cerr << "Usage: " << name << " "
              << "Options:\n"
              << std::endl
              << "\t-v,--verbose Show debug output" << std::endl
              << "\t-p,--password,[password] Password Mode" << std::endl
              << "\t-a,--account [account]" << std::endl
              << "\t-h,--help\t\tShow help\n"
              << std::endl;
}

/**
 * @brief Processes command line arguments for the application.
 *
 * This function examines each command-line argument provided to the application. It supports various
 * options including help, verbose logging, password setting for SSH, and specifying an IP address.
 * The function returns different status codes based on the outcome of argument processing.
 *
 * @param argc The number of command-line arguments.
 * @param argv An array of character pointers listing all the arguments.
 * @return Returns 1 if help is requested, -1 if there is an error in processing some arguments,
 *         and 0 if all arguments are processed successfully.
 *
 */
int handleArguments(int argc, char *argv[])
{

    LOG4CXX_DEBUG(LSPAppLogger, "Received " << argc - 1 << " arguments");

    // check each command line argument
    for (int i = 1; i < argc; ++i)
    {
        std::string arg = argv[i];

        LOG4CXX_DEBUG(LSPAppLogger, "Argument: " << arg);

        if ((arg == "-h") || (arg == "--help"))
        {
            show_usage(argv[0]);
            return 1;
        }
        else if ((arg == "-ip") || (arg == "--ipaddr"))
        {
            std::string hostIPAddr;
            if (LSPApp::getNextArg(i, argc, argv, hostIPAddr))
            {
                LSPApp::setHostDockerIPAddr(hostIPAddr);
                LOG4CXX_INFO(LSPAppLogger, "Host IP Address: " << LSPApp::getHostDockerIPAddr());
            }
            else
                return -1;
        }
    }
    return 0;
}

/**
 * @brief Function to decode a Base64 encoded string.
 *
 * This function decodes a Base64 encoded string and returns the decoded result.
 *
 * @param input The Base64 encoded string to be decoded.
 * @return The decoded string.
 */

std::string base64Decode(const std::string &input)
{
    BIO *bio, *b64;
    char buffer[input.size()];
    memset(buffer, 0, sizeof(buffer));

    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new_mem_buf(input.c_str(), input.size());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL); // Ignore newlines
    int length = BIO_read(bio, buffer, input.size());
    BIO_free_all(bio);

    return std::string(buffer, length);
}

// Function to decrypt data
std::string decrypt(const std::string &base64_encrypted_data)
{
    auto encrypted_data = base64Decode(base64_encrypted_data);
    auto key = base64Decode(JSONEncryptionKey);

    // Extract IV and ciphertext from the encrypted data
    std::vector<unsigned char> iv(encrypted_data.begin(), encrypted_data.begin() + AES_BLOCK_SIZE);
    std::vector<unsigned char> ciphertext(encrypted_data.begin() + AES_BLOCK_SIZE, encrypted_data.end());

    // Create and initialise the context
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    if (!ctx)
        throw std::runtime_error("Failed to create EVP_CIPHER_CTX");

    // Initialise the decryption operation
    if (1 != EVP_DecryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, (const unsigned char *)key.data(), (const unsigned char *)iv.data()))
    {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("EVP_DecryptInit_ex failed");
    }

    // Provide the message to be decrypted, and obtain the plaintext output
    std::vector<unsigned char> plaintext(ciphertext.size() + AES_BLOCK_SIZE);
    int len = 0;
    int plaintext_len = 0;

    if (1 != EVP_DecryptUpdate(ctx, plaintext.data(), &len, ciphertext.data(), ciphertext.size()))
    {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("EVP_DecryptUpdate failed");
    }
    plaintext_len += len;

    if (1 != EVP_DecryptFinal_ex(ctx, plaintext.data() + plaintext_len, &len))
    {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("EVP_DecryptFinal_ex failed");
    }
    plaintext_len += len;

    // Clean up
    EVP_CIPHER_CTX_free(ctx);

    // Return the resulting plaintext as a string
    return std::string(plaintext.begin(), plaintext.begin() + plaintext_len);
}

const std::string DecryptEnrollmentJSON(const std::string &encryptedEnrollmentJSONString)
{

    json encryptedJSON = json::parse(encryptedEnrollmentJSONString);

    // Extract the 'lighthouse_enrollment' object for easier handling
    json &encryptedEnrollmentJSON = encryptedJSON["lighthouse_enrollment"];

    // New JSON document
    json decryptedJson;
    decryptedJson["lighthouse_enrollment"] = json::object();

    LOG4CXX_INFO(LSPAppLogger, encryptedEnrollmentJSON);

    // Iterate over all key-value pairs (tags) in the original JSON
    for (auto &element : encryptedEnrollmentJSON.items())
    {

        if ((element.key() == "address") || (element.key() == "token") || (element.key() == "bundle"))
        {
            std::string encryptedValue = element.value().get<std::string>();

            const std::string decryptedValue = decrypt(encryptedValue);

            decryptedJson["lighthouse_enrollment"][element.key()] = decryptedValue;
        }
        else
            decryptedJson["lighthouse_enrollment"][element.key()] = element.value();
    }

    return decryptedJson.dump();
}

/**
 * @brief Main entry point for the LH Enrollment application.
 *
 * This function initializes the application by handling command-line arguments.
 * Depending on the compilation flags, it either triggers enrollment using the NGCS REST API or reads
 * enrollment data from standard input and writes it to a file. It also signals the completion of the
 * enrollment process via a FIFO. The function logs its start and successful exit and catches any
 * exceptions that occur during execution.
 *
 * @param argc The number of command-line arguments.
 * @param argv An array of character pointers listing all the arguments.
 * @return Returns 0 on successful execution, and -1 if an exception occurs.
 *
 */
int main(int argc, char *argv[])
{
    try
    {
        if (handleArguments(argc, argv) == 0)
        {

            LOG4CXX_INFO(LSPAppLogger, "LH Enrollment App Started");

            // Read Lighthouse Enrollment JSON from piped input
            std::istreambuf_iterator<char> begin(std::cin), end;
            std::string encryptedEnrollmentJSON(begin, end);

            LOG4CXX_INFO(LSPAppLogger, encryptedEnrollmentJSON);

            // Decrypt the obscured field values
            std::string enrollmentJSON = DecryptEnrollmentJSON(encryptedEnrollmentJSON);

            LOG4CXX_INFO(LSPAppLogger, "Saving Enrollment Data");

            LSPApp::WriteStringToFile(enrollmentJSON, LSPEnrollmentFilePath, "Lighthouse Enrollment Data");

            // Trigger the LSP Bootstrap which checks for the state of the enrollment
            SendEnrollmentFifo("Enrollment Triggered");
        }

        LOG4CXX_INFO(LSPAppLogger, "Exited normally");

        return 0; // Success
    }
    catch (const std::exception &exc)
    {
        // catch anything thrown within try block that derives from std::exception
        LOG4CXX_FATAL(LSPAppLogger, exc.what());
    }

    return -1; // Failure
}
