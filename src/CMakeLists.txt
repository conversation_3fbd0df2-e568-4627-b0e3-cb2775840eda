cmake_minimum_required(VERSION 3.5)

include(FindPkgConfig)

option(TARGET_ARCH "Target architecture" "x86_64") # Default to x86_64

# Set the path to the toolchain file based on the selected architecture
if(TARGET_ARCH STREQUAL "x86_64")
  include ("${CMAKE_CURRENT_SOURCE_DIR}/toolchain-x86-64.cmake")

elseif(TARGET_ARCH STREQUAL "armv7")
  include ("${CMAKE_CURRENT_SOURCE_DIR}/toolchain-armv7.cmake")

elseif(TARGET_ARCH STREQUAL "armv8")
  include ("${CMAKE_CURRENT_SOURCE_DIR}/toolchain-armv8.cmake")
endif()

project("LSP Applications")

set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--strip-all")

# Find required packages
find_package(PkgConfig REQUIRED)

find_package(CURL REQUIRED)
if(NOT CURL_FOUND)
    message(FATAL_ERROR "CURL library not found")
endif()

pkg_check_modules(CURLPP REQUIRED curlpp)

set(CMAKE_CXX_STANDARD 11) # C++11...
set(CMAKE_CXX_STANDARD_REQUIRED ON) #...is required...
set(CMAKE_CXX_EXTENSIONS OFF) #...without compiler extensions like gnu++11

# Include directories for curl
include_directories(${CURL_INCLUDE_DIRS})

# Link the libraries
link_libraries(${CURLPP_LDFLAGS})
link_libraries(${CURL_LIBRARIES} ${CURLPP_LIBRARIES})

# Attempt to find the log4cxx library
find_library(LOG4CXX_LIBRARIES  NAMES log4cxx  PATHS /usr/local/lib)

# Print the OpenSSL include directory
message(STATUS "OpenSSL include dir: ${OPENSSL_INCLUDE_DIR}")

set(CMAKE_PREFIX_PATH /usr/local/lib/cmake)

# Find Doxygen
find_package(Doxygen)

if(DOXYGEN_FOUND)
    # Define the custom target
    add_custom_target(doc_doxygen 
        COMMAND ${DOXYGEN_EXECUTABLE} Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM)
endif()

# Define the base directory to search for source files
set(SOURCE_DIR ${CMAKE_SOURCE_DIR})

# Find all .cpp files in the specified directory and subdirectories
file(GLOB_RECURSE CPP_SOURCES "${SOURCE_DIR}/*.cpp")

# Create a script that runs clang-tidy on all sources
set(TIDY_SCRIPT "${CMAKE_BINARY_DIR}/run_clang_tidy.sh")

file(WRITE ${TIDY_SCRIPT} "#!/bin/sh\n")

# Specify the checks you want to run
set(MY_CLANG_TIDY_CHECKS "-*,clang-analyzer-cplusplus.NewDelete,clang-analyzer-cplusplus.NewDeleteLeaks,readability-*,misc-unused-using-decls,misc-unused-parameters,misc-unused-alias-decls,clang-analyzer-core.UninitializedVariables,clang-analyzer-valist.Uninitialized")

# Append clang-tidy command for each source file to the script
foreach(SOURCE_FILE ${CPP_SOURCES})
  file(APPEND ${TIDY_SCRIPT}
        "echo Running clang-tidy on ${SOURCE_FILE}\n"
        "clang-tidy -checks='${MY_CLANG_TIDY_CHECKS}' ${SOURCE_FILE} -- -std=c++17 "
        "-I/usr/include/c++/9 "
        "-I/usr/include/x86_64-linux-gnu/c++/9 "
        "-I${CMAKE_SOURCE_DIR}/LSPAppLib/libsshcpp "
        "-I${CMAKE_SOURCE_DIR}/LSPAppLib "
	"-I${CMAKE_SOURCE_DIR}/AWS-PCA-CertManager "
	"-I${CMAKE_SOURCE_DIR}/json/include\n"
    )
endforeach()

# Make the script executable
file(CHMOD ${TIDY_SCRIPT} PERMISSIONS OWNER_EXECUTE OWNER_WRITE OWNER_READ)

# Add a custom target that runs the generated script
add_custom_target(
    clang_tidy_all
    COMMAND ${TIDY_SCRIPT}
    COMMENT "Running clang-tidy on all project files"
    )

# Include files
include_directories(${PROJECT_SOURCE_DIR}/LSPAppLib)                                                        
include_directories(${PROJECT_SOURCE_DIR}/LSPAppLib/libsshcpp)
include_directories(${PROJECT_SOURCE_DIR}/AWS-PCA-CertManager)
include_directories(${PROJECT_SOURCE_DIR}/json/include)

# OpenSSL might not be the system version so this is needed
include_directories(${OPENSSL_INCLUDE_DIR})

# Link against log4cpp
link_libraries(${LOG4CXX_LIBRARIES})

# LSPAppLib and NGCSHostAPI are here
link_directories(LSPAppLib)

# Define project subdirectories - These components are used by the NGCS LSP App
add_subdirectory(LSPAppLib)
add_subdirectory(AWS-PCA-CertManager)
add_subdirectory(LSPEnrollmentApp)
add_subdirectory(LSPBootstrapApp)
add_subdirectory(LSPStatusApp)
add_subdirectory(GetLSPCertApp)

