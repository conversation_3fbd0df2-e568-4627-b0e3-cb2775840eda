/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: March 13th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal NGCS Container Bootstrap Application
 */

// C++ Include Files
#include <thread>
#include <iostream>
#include <string>
#include <sstream>
#include <fstream>
#include <cstring>
#include <ctime>
#include <cstdlib>
#include <unistd.h>

// log4cpp include files
#include <log4cxx/logger.h>

// Include the LSP Library header
#include "LSPAppLib.hpp"

// Include the LSP Status Sodes
#include "../LSPStatusApp/LSPStatusApp.h"

// Include header files for NGCS TPM API
#include "NGCSTPM.h"

// Include the TPM config (handles)
#include "TPMConfig.h"

// Include header file for AWS Private Certificate Authority API
#include "AWSCertificateMgr.h"

// Include Edge settings for LSP
#include "LSPEdgeConfig.h"

// Location of the intermediate CA Cert. This is installed from the Dockerfile
#define LSP_PROD_INTERMEDIATE_CERT_FILE "/etc/ssl/certs/LSP-intermediate-ca.crt"
#define LSP_STAGING_INTERMEDIATE_CERT_FILE "/etc/ssl/certs/LSP-staging-intermediate-ca.crt"

// Default EdgeIQ account name
std::string edgeIQAccountName = LSP_EDGE_ACCOUNT_NAME;

// Default EdgeIQ domain - the LSP sub-account name is prepended when we create a CSR
const std::string rootDomain = LSP_EDGE_ROOT_DOMAIN;

// The logger is used by the library code so it is declared and initialized there and extern here
log4cxx::LoggerPtr LSPAppLogger = LSPApp::getLogger("LSP Application");

// Default behavior is to automatically call home to EdgeIQ
bool automaticReqEnrollment = true;

enum class LSPFifoResult
{
    Unknown,
    LSPTimerFired,
    EnrollmentTriggered
};

using namespace std;

/**
 * @brief Runs the EdgeIQ Agent using the specified EdgeIQ account or sub-account
 *
 * This function initiates the EdgeIQ Agent for a specified Edge IQ account by setting necessary
 * environment variables and using a fork-exec pattern to create a new process for the agent.
 *
 * It sets the EDGE_COMPANY and EDGE_UNIQUE_ID environment variables to the provided account name
 * and serial number, respectively (this is the interface provided by EdgeIQ).
 *
 * The fork-exec pattern ensures that the external program is started securely, avoiding shell
 * interpretation vulnerabilities.

 *
 * This function starts the EdgeIQ Agent by setting environment variables
 * and using fork() and exec() to create a new process. The method attempts
 * to run the agent with root privileges and logs relevant information using
 * the LOG4CXX framework.
 *
 * @param edgeIQAccountName The account name to be used by the EdgeIQ Agent.
 * @param serialNumber The unique identifier for the EdgeIQ Agent instance.
 *
 * @warning This function uses the `setenv` function to set environment variables
 *
 * @return Returns 0 if the EdgeIQ Agent is started successfully, -1 if there is
 *         an error in starting the process or escalating privileges.
 *
 * @note The function logs information, success, and error messages through the logging system.
 **/

int RunEdgeIQAgent(const std::string &edgeIQAccountName, const std::string &serialNumber)
{

    LOG4CXX_INFO(LSPAppLogger, "Starting LSP Agent");
    LOG4CXX_INFO(LSPAppLogger, "Account: '" << edgeIQAccountName << "' Unique ID: '" << serialNumber);

    // Set environment variables. It's safer and more efficient than concatenating command strings.
    setenv("EDGE_COMPANY", edgeIQAccountName.c_str(), 1);
    setenv("EDGE_UNIQUE_ID", serialNumber.c_str(), 1);

    // Use fork and exec to start the external program. This avoids shell interpretation vulnerabilities.
    pid_t pid = fork();

    if (pid == -1)
    {
        LOG4CXX_ERROR(LSPAppLogger, "Unable to start LSP Agent. Fork failed");
        return -1;
    }
    else if (pid > 0)
    {
        LOG4CXX_INFO(LSPAppLogger, "LSP Agent started");
        // Parent process - No need to wait for the child, just return success
        return 0;
    }
    else
    {

        // Child process - escalate to root
        if (setuid(0) == -1)
        {
            LOG4CXX_ERROR(LSPAppLogger, "Failed to escalate to root user");
            return -1;
        }

        // Child process - Execute the Edge IQ Agent.
        execl(EDGE_IQ_AGENT_APP, "edge", (char *)NULL);

        // execl only returns on error
        LOG4CXX_ERROR(LSPAppLogger, "Unable to start LSP Agent. execel failed");
        return -1;
    }
}

/**
 * @brief Triggers a "LSP Call Home" action for the device.
 *
 * This function initiates a "LSP Call Home" action by sending a request to a predefined URL. The
 * URL Endpoint is defined on the EdgeIQ cloud and is currently configured to be Port TCP/8084.
 *
 * It periodically retries the request for a total duration, initially retrying every 10 seconds
 * for the first 2 minutes, then increasing the interval to 30 seconds. The serial number is no
 * longer sent with the request, as EdgeIQ now relies on the device's certificate subject for
 * identification.
 *
 * @return Returns 0 if the "Call Home" action is successfully triggered. If an exception occurs
 * during the request process, the function logs the error and continues to retry. If the loop
 * exits without a successful request (which theoretically should not happen given the infinite loop),
 * it returns -1.
 *
 * @note The function logs the status of the automatic call home feature at the start, as well as
 * informational messages about retry intervals and the initiation of the "Call Home" action. It logs
 * any exceptions caught during the request process as fatal errors.
 *
 * @warning The function enters an infinite loop, relying on the successful completion of the request
 * or an external condition to break the loop. This design assumes that the "Call Home" action will
 * eventually succeed or that the device will otherwise intervene. It is essential to monitor the
 * behavior of this function to prevent indefinite operation in the case of persistent failures.
 */

int TriggerReqEnrollment(void)
{

    LOG4CXX_INFO(LSPAppLogger, "Automatic Call Home is " << (automaticReqEnrollment ? "ENABLED" : "DISABLED"));

    // Create JSON object
    json callHomeJSON;

    // Sending the device serial number was deprecated when mTLS was added.
    // Now EdgeIQ gets the serial number from the certificate subject
    callHomeJSON["SN"] = "empty";

    const std::string callHomeURL = std::string(POST_CALL_HOME_URL);

    const int totalWaitTime = 2 * 60; // 2 minutes
    unsigned int waitTimeSeconds = 10;
    unsigned int totalSeconds = 0;

    // Retry ever 10 seconds for the 2 minutes minutes. Retry every 30 seconds after that
    while (true)
    {
        try
        {
            // Wait 10 seconds for EdgeIQ to connect
            std::this_thread::sleep_for(std::chrono::seconds(waitTimeSeconds));
            totalSeconds += waitTimeSeconds;

            if (totalSeconds > totalWaitTime)
            {
                waitTimeSeconds = 30; // Backoff to trying every 30 seconds
                LOG4CXX_INFO(LSPAppLogger, "Retry every " << waitTimeSeconds << " seconds");
            }

            LOG4CXX_INFO(LSPAppLogger, "Requesting Enrollment Data");

            std::string response = LSPApp::postJSONToURL(callHomeURL, callHomeJSON.dump());

            LOG4CXX_DEBUG(LSPAppLogger, "Call Home Response: " + response);

            return 0;
        }
        catch (const std::exception &exc)
        {
            // catch anything thrown within try block that derives from std::exception
            LOG4CXX_FATAL(LSPAppLogger, exc.what());
        }
    }
    return -1;
}

/**
 * @brief Checks if the automatic "Call Home" feature is enabled.
 *
 * This method determines the state of the automatic "Call Home" feature by checking
 * the environment variable "LSP_TRIGGER". If "LSP_TRIGGER" is set to "DISABLE", the
 * feature is considered disabled, and the method returns false. Otherwise, the feature
 * is considered enabled, and the method returns true. The default state of the
 * automatic call home feature is controlled by the `automaticReqEnrollment` class member,
 * which should be set to true unless explicitly disabled.
 *
 * @return true if the automatic "Call Home" feature is enabled, false otherwise.
 *
 * @note The method logs the value of the "LSP_TRIGGER" environment variable if it is set,
 * aiding in diagnosing the operational state of the "Call Home" feature. It is important to
 * ensure that this environment variable is correctly configured in the system or application
 * environment to control the feature's behavior as expected.
 *
 * @warning Modifying the "LSP_TRIGGER" environment variable while the application is running
 * may not affect the feature's state if the environment variable is only checked at startup.
 * To dynamically control the feature, additional mechanisms may be required.
 */

bool AutomaticReqEnrollmentIsEnabled(void)
{

    const char *lspTriggerEnv = getenv("LSP_TRIGGER");
    if (lspTriggerEnv != NULL)
    {

        const std::string LSPTrigger(lspTriggerEnv);
        LOG4CXX_INFO(LSPAppLogger, "LSP Automatic Environment Var: " << LSPTrigger);

        if (LSPTrigger == "DISABLE")
            automaticReqEnrollment = false;
    }

    return automaticReqEnrollment;
}

const std::string GetLSPStatusValue(const LSPStatus &lspStatus)
{

    switch (lspStatus)
    {
    case LSPStatus::Unknown:
        return "Unknown state";
    case LSPStatus::LSPDisabled:
        return "LSPDisabled";
    case LSPStatus::CalledHome:
        return "CalledHome";
    case LSPStatus::MissingSN:
        return "MissingSN";
    case LSPStatus::MissingLHData:
        return "MissingLHData";
    case LSPStatus::EnrollmentTriggered:
        return "EnrollmentTriggered";
    case LSPStatus::Enrolling:
        return "Enrolling";
    case LSPStatus::EnrollmentFailed:
        return "EnrollmentFailed";
    case LSPStatus::Enrolled:
        return "Enrolled";
    case LSPStatus::EdgeIQUnreachable:
        return "EdgeIQUnreachable";
    case LSPStatus::SFUnreachable:
        return "SFUnreachable";
    default:
        return "InvalidState";
    }
}

const std::string GetLSPStatusMessage(const LSPStatus &lspStatus)
{

    switch (lspStatus)
    {
    case LSPStatus::Unknown:
        return "Unknown state";
    case LSPStatus::LSPDisabled:
        return "LSP disabled state";
    case LSPStatus::CalledHome:
        return "Enrollment Requested";
    case LSPStatus::MissingSN:
        return "Missing serial number state";
    case LSPStatus::MissingLHData:
        return "Missing LH data state";
    case LSPStatus::EnrollmentTriggered:
        return "Enrollment triggered state";
    case LSPStatus::Enrolling:
        return "Enrolling state";
    case LSPStatus::EnrollmentFailed:
        return "Enrollment failed state";
    case LSPStatus::Enrolled:
        return "Enrolled state";
    case LSPStatus::EdgeIQUnreachable:
        return "EdgeIQ unreachable state";
    case LSPStatus::SFUnreachable:
        return "SF unreachable state";
    default:
        return "Invalid state";
    }
}

int SendLSPStatus(const std::string &serialNumber, const LSPStatus &lspStatus)
{

    // Get the current date and time
    auto now = std::chrono::system_clock::now();
    auto now_time = std::chrono::system_clock::to_time_t(now);
    std::string dateTime = std::ctime(&now_time);
    dateTime.pop_back(); // Remove the trailing newline character
    json LSPStatusJSON = {
        {"deviceStatus", {{"serialNumber", serialNumber}, {"dateTime", dateTime}, {"status", GetLSPStatusValue(lspStatus)}, {"statusMessage", GetLSPStatusMessage(lspStatus)}}}};

    const int totalWaitTime = 2 * 60; // 2 minutes
    unsigned int waitTimeSeconds = 10;
    unsigned int totalSeconds = 0;

    LOG4CXX_INFO(LSPAppLogger, "Sending " << LSPStatusJSON.dump());

    while (true)
    {
        try
        {
            // Wait 10 seconds for EdgeIQ to connect
            std::this_thread::sleep_for(std::chrono::seconds(waitTimeSeconds));
            totalSeconds += waitTimeSeconds;

            if (totalSeconds > totalWaitTime)
            {
                waitTimeSeconds = 30; // Backoff to trying every 30 seconds
                LOG4CXX_INFO(LSPAppLogger, "Retry every " << waitTimeSeconds << " seconds");
            }

            LOG4CXX_INFO(LSPAppLogger, "Sending JSON Status");

            const std::string response = LSPApp::postJSONToURL(POST_STATUS_URL, LSPStatusJSON.dump());

            LOG4CXX_DEBUG(LSPAppLogger, "Sending JSON Status: " + response);

            return 0;
        }
        catch (const std::exception &exc)
        {
            // catch anything thrown within try block that derives from std::exception
            LOG4CXX_FATAL(LSPAppLogger, exc.what());
        }
    }
    return -1;
}

const std::string ReadLSPIntermediateCACertFromFile(const std::string &EdgeIQAccountName)
{
    if (EdgeIQAccountName == "lsppoc")
    {
        // Read the Intermediate CA Cert from the file system
        std::string StagingIntermediateCACert = LSPApp::ReadStringFromFile(LSP_STAGING_INTERMEDIATE_CERT_FILE);
        if (StagingIntermediateCACert.empty())
        {
            throw std::runtime_error("Failed to read LSP Staging Intermediate CA Certificate");
        }
        return StagingIntermediateCACert;
    }

    if (edgeIQAccountName == "lsp")
    {
        // Read the Intermediate CA Cert from the file system
        std::string ProdIntermediateCACert = LSPApp::ReadStringFromFile(LSP_PROD_INTERMEDIATE_CERT_FILE);
        if (ProdIntermediateCACert.empty())
        {
            throw std::runtime_error("Failed to read LSP Production Intermediate CA Certificate");
        }
        return ProdIntermediateCACert;
    }

    throw std::runtime_error("Invalid LSP Account name");
}

void DeployEdgeIQConfigFile(const std::string &EdgeIQAccountName)
{
    // Use the LSP Staging Config file
    if (EdgeIQAccountName == "lsppoc")
    {
        if (LSPApp::FileExists(EDGE_IQ_STAGING_CONF_FILE))
        {
            std::remove(EDGE_IQ_CONF_FILE);
            if (symlink(EDGE_IQ_STAGING_CONF_FILE, EDGE_IQ_CONF_FILE) == 0)
                return;
            throw std::runtime_error("Error creating symbolic link for the LSP Staging config file");
        }
        else
        {
            LOG4CXX_INFO(LSPAppLogger, EDGE_IQ_STAGING_CONF_FILE);
            throw std::runtime_error("Missing LSP Config file for staging environment");
        }
    }

    // Use the LSP Production Config file
    if (EdgeIQAccountName == "lsp")
    {
        if (LSPApp::FileExists(EDGE_IQ_PROD_CONF_FILE))
        {
            std::remove(EDGE_IQ_CONF_FILE);
            if (symlink(EDGE_IQ_PROD_CONF_FILE, EDGE_IQ_CONF_FILE) == 0)
                return;

            throw std::runtime_error("Error creating symbolic link for the LSP Production config file");
        }
        else
        {
            LOG4CXX_INFO(LSPAppLogger, EDGE_IQ_PROD_CONF_FILE);
            throw std::runtime_error("Missing LSP Config file for production environment");
        }
    }

    throw std::runtime_error("Invalid LSP Account name");
}

/**
 * @brief Handles the "Call Home" process for EdgeIQ using the device's LSP X.509 certificate.
 *
 * This function constructs a domain using the EdgeIQ account name and verifies the device's LSP X.509 certificate.
 * If the certificate is valid, it initiates the EdgeIQ agent and potentially triggers a "Call Home" operation if
 * automatic calling is enabled. The function throws an exception if the certificate validation fails.
 *
 * @param edgeIQAccountName The EdgeIQ account name used to construct the domain for certificate validation.
 * @param serialNumber The serial number of the device used in certificate validation.
 *
 * @throws std::runtime_error if the device's LSP certificate is invalid.
 *
 * @note This function assumes that `rootDomain` is a global or externally defined variable that is combined with
 *       the account name to form the full domain name for certificate validation.
 */
void HandleEdgeIQEnrollment(const std::string &edgeIQAccountName, const std::string &serialNumber)
{

    // Append the EdgeIQ Account name to the domain
    const std::string domain = edgeIQAccountName + "." + rootDomain;

    AWSCertificateMgr AWSCertificateMgr;

    // Validate the device's LSP X.509 certificate
    if (AWSCertificateMgr.CheckDeviceCertificateInTPM(serialNumber, domain) == AWSCertificateMgr::CertificateValidity::IsNOTValid)
    {
        throw std::runtime_error("LSP Certificate is invalid");
    }

    NGCSTPM NGCSTPMAbstraction;

    // Read the LSP device Cert from the TPM
    std::string LSPCertificateData =
        NGCSTPMAbstraction.GetLSPCertificateFromTPM(LSPApp::parseHexString(LSP_TPM_Certificate_Handle));

    if (LSPCertificateData.empty())
    {
        throw std::runtime_error("Failed to read LSP Certificate");
    }

    const std::string LSPCertificate(LSPCertificateData.begin(), LSPCertificateData.end());

    const std::string IntermediateCACert = ReadLSPIntermediateCACertFromFile(edgeIQAccountName);

    std::ostringstream certBundleStream;
    certBundleStream << LSPCertificate << std::endl
                     << IntermediateCACert;

    const std::string LSPBundleFile = "/opt/LSP/pki/" + serialNumber + ".bundle";

    LSPApp::WriteStringToFile(certBundleStream.str(), LSPBundleFile, "LSP Device Certificate");

    const std::string EdgeIQCertFile = "/opt/LSP/pki/opengear.devices.ai.crt";

    // Create symbolic link for the EdgeIQ certificate bundle so the EdgeIQ agent can read it
    if (!LSPApp::FileExists(EdgeIQCertFile.c_str()))
    {
        int result = symlink(LSPBundleFile.c_str(), EdgeIQCertFile.c_str());
        if (result != 0)
        {
            throw std::runtime_error("Error creating symbolic link for certificate bundle");
        }
    }

    DeployEdgeIQConfigFile(edgeIQAccountName);

    // LSP Certificate is valid so start the Edge IQ Agent
    const int edgeStatus = RunEdgeIQAgent(edgeIQAccountName, serialNumber);

    // Trigger Call Home via EdgeIQ
    if (AutomaticReqEnrollmentIsEnabled())
    {
        TriggerReqEnrollment();
        SendLSPStatus(serialNumber, LSPStatus::CalledHome);
    }
}

/**
 * @brief Retrieves the serial number of the device from a specific file.
 *
 * This function reads the device's serial number from a predefined file location.
 * The function assumes that the serial number is stored in a plain text format
 * within the file.
 *
 * @return Returns a string containing the serial number of the device.
 *
 * @note This function will return an empty string if the file does not exist or
 *       cannot be read. Ensure that the file path is correct and accessible.
 */

const std::string GetDeviceSerialNumber(void)
{
    // First try the legacy method for backwards compatibility
    try
    {
        const std::string legacySMFile = "/opt/LSP/sys/firmware/vpd/ro/serial_number";

        // Try to read the serial number from the OM family location
        const std::string OMSerialNumber = LSPApp::ReadStringFromFile(legacySMFile);
        if (!OMSerialNumber.empty())
            return OMSerialNumber;

        throw std::runtime_error("Could not find serial number in " + legacySMFile);
    }

    // Couldn't open the serial number from the OM family location
    catch (const std::exception &exc)
    {
        const char *SerialNumberEnv = std::getenv("SERIAL_NUMBER"); // Read the serial number from the environment variable

        if (SerialNumberEnv)
            return (std::string(SerialNumberEnv)); // Convert to std::string

        throw std::runtime_error("Could not find serial number in SERIAL_NUMBER env var");
    }
}

/**
 * @brief Checks the enrollment status by reading from a FIFO.
 *
 * This function opens and reads from a FIFO specified by LSP_ENROLLMENT_FIFO.
 * It continuously reads lines from the FIFO and checks for a specific message
 * indicating that enrollment has been triggered. If the message is found,
 * the function returns true. If the end of the FIFO is reached without finding
 * the message, it returns false.
 *
 * @return Returns true if the "Enrollment Triggered" message is found, otherwise returns false.
 * @throws std::runtime_error if the FIFO file cannot be opened.
 *
 * @note This function blocks and reads the FIFO until the "Enrollment Triggered" message is found
 *       or until there are no more messages to read. Ensure that the FIFO is properly created and accessible.
 */
LSPFifoResult ReadEnrollmentStatusFromFIFO(void)
{

    std::ifstream fifo(LSP_ENROLLMENT_FIFO);

    if (!fifo.is_open())
    {
        throw std::runtime_error("Failed to open FIFO: " LSP_ENROLLMENT_FIFO);
    }

    std::string message;

    while (std::getline(fifo, message))
    {

        if (message == "Enrollment Triggered")
        {
            return LSPFifoResult::EnrollmentTriggered;
        }

        if (message == "LSP Timer Fired")
        {
            return LSPFifoResult::LSPTimerFired;
        }
    }
    return LSPFifoResult::Unknown;
}

/**
 * @brief Blocks and waits indefinitely for enrollment data from EdgeIQ.
 *
 * This function continuously checks for a signal indicating that enrollment data has been sent
 * to the LSPApp. It utilizes a FIFO to receive the signal "Enrollment Triggered". If this signal
 * is received, it logs the event and returns true.
 *
 * @return Returns true if "Enrollment Triggered" is received, otherwise it should theoretically never return false
 *         since the loop does not break unless the condition is met.
 *
 * @note This function will block indefinitely unless the "Enrollment Triggered" message is received. It depends on
 *       the correct operation of the ReadEnrollmentStatusFromFIFO() function to detect the signal.
 */

bool WaitForEnrollmentData(const std::string &serialNumber)
{

    while (true)
    {
        // Wait for EdgeIQ to send the Entollment Data to the LSPApp. It signals us by sending "Enrollment Triggered" using a FIFO
        const LSPFifoResult FIFOResult = ReadEnrollmentStatusFromFIFO();

        if (FIFOResult == LSPFifoResult::EnrollmentTriggered)
        {
            LOG4CXX_INFO(LSPAppLogger, "Enrollment triggered");
            SendLSPStatus(serialNumber, LSPStatus::EnrollmentTriggered);
            return true;
        }
        else if (FIFOResult == LSPFifoResult::LSPTimerFired)
        {
            LOG4CXX_INFO(LSPAppLogger, "Container will terminate");

            return true;
        }
    }

    return false;
}

/**
 * @brief Displays usage information for the application.
 *
 * This function prints the command-line usage instructions for the application,
 * detailing available options and their descriptions. It is typically called when
 * the user passes the '-h' or '--help' option or provides invalid arguments.
 *
 * @param name The name of the application executable. This is used to instruct
 * the user on how to run the application from the command line.
 */

void show_usage(const std::string &name)
{
    std::cout << "Usage: " << name << " [option(s)]\n"
              << "Options:\n"
              << "\t-h,--help\t\tShow this help message\n"
              << "\t-v,--verbose\t\tEnable verbose logging\n"
              << "\t-t,--trigger\t\tEnable automatic call home\n"
              << "\t-a,--account NAME\tSpecify the account name\n"
              << "\t-ip,--ipaddr ADDRESS\tSpecify the IP address\n"
              << std::endl;
}

/**
 * @brief Processes command line arguments passed to the application.
 *
 * This function iterates over each argument passed to the application, logs each one, and performs
 * actions based on specific flags. It handles standard options like '-h' (help) and can also handle
 * custom options like '-v' (verbose), '-t' (trigger), '-a' (account), and '-ip' (IP address).
 * The function returns early with a status code of 1 if it encounters any issues with the arguments
 * or if the help message is triggered.
 *
 * @param argc The number of command-line arguments.
 * @param argv An array of character pointers listing all the arguments.
 * @return Returns 0 if all arguments are processed successfully, otherwise returns 1.
 *
 * @note This function makes extensive use of logging for debugging purposes and assumes the existence
 *       of a global logger, LSPAppLogger.
 */
int handleArguments(int argc, char *argv[])
{
    LOG4CXX_DEBUG(LSPAppLogger, "Received " << argc - 1 << " arguments");

    for (int i = 1; i < argc; ++i)
    {
        std::string arg = argv[i];

        LOG4CXX_DEBUG(LSPAppLogger, "Argument: " << arg);

        if (arg == "-h" || arg == "--help")
        {
            show_usage(argv[0]);
            return 1; // Failure
        }
        else if (arg == "-t" || arg == "--trigger")
        {
            automaticReqEnrollment = true;
        }
        else if (arg == "-a" || arg == "--account")
        {
            std::string accountName;
            if (LSPApp::getNextArg(i, argc, argv, accountName))
            {
                edgeIQAccountName = accountName;
                LOG4CXX_INFO(LSPAppLogger, "Using LSP Account name " << edgeIQAccountName);
            }
            else
            {
                return 1; // Failure
            }
        }
        else if (arg == "-ip" || arg == "--ipaddr")
        {
            std::string ipAddr;
            if (LSPApp::getNextArg(i, argc, argv, ipAddr))
            {
                LSPApp::setHostDockerIPAddr(ipAddr);
                LOG4CXX_INFO(LSPAppLogger, "Host IP Address: " << LSPApp::getHostDockerIPAddr());
            }
            else
            {
                return 1; // Failure
            }
        }
        else
        {
            LOG4CXX_ERROR(LSPAppLogger, "Unknown option: " << arg);
            show_usage(argv[0]);
            return 1; // Failure
        }
    }

    return 0; // Success
}

/**
 * @brief Manages device operations when not using the NGCS API.
 *
 * This function retrieves the device's serial number from the local file system and verifies its presence.
 * If the serial number is missing, it throws an exception. Once the serial number is confirmed, it triggers
 * the EdgeIQ agent for operations such as call home and waits for enrollment data to be sent and processed.
 *
 * @throws std::runtime_error If the device serial number is missing, indicating critical information
 *         needed for device operations is unavailable.
 *
 * @note This function is critical for initializing and managing device operations in environments where
 *       NGCS API is not utilized. It relies on local system properties and assumes that the EdgeIQ agent
 *       and relevant data paths are properly configured.
 */
void LSPRun(void)
{

    // Get the device serial number from the NGCS file system
    const std::string serialNumber = GetDeviceSerialNumber();

    if (serialNumber.empty())
    {
        throw std::runtime_error("Device serial number is missing");
    }

    LOG4CXX_INFO(LSPAppLogger, "Device serial number is " << serialNumber);

    // Start the EdgeIQ Agent and trigger call home
    HandleEdgeIQEnrollment(edgeIQAccountName, serialNumber);

    // Wait for EdgeIQ to send the Enrollment data. The data is written to /opt/LSP/results directory
    // The NGCS hosts picks up the enrollment data and triggers the enrollment
    WaitForEnrollmentData(serialNumber);
}

// Function to be called when the timer ends
void onTimerEnd(void)
{

    LOG4CXX_INFO(LSPAppLogger, "LSP timer fired");

    std::ofstream fifo(LSP_ENROLLMENT_FIFO);

    if (!fifo.is_open())
    {
        throw std::runtime_error("Failed to open FIFO: " LSP_ENROLLMENT_FIFO);
    }

    fifo << "LSP Timer Fired";
}

// Function to start a timer for a given duration
void startTimer(std::chrono::minutes duration, std::function<void()> callback)
{
    std::this_thread::sleep_for(duration);
    callback();
}

void StartLSPContainerTimer(void)
{

    LOG4CXX_INFO(LSPAppLogger, "Container will automatically terminate in 24 hours");

    // Create a thread to run the timer
    std::thread timerThread(startTimer, std::chrono::hours(24), onTimerEnd);

    // Detach the thread to allow it to run independently
    timerThread.detach();

    // Do other stuff in the main thread if needed
    std::cout << "Timer is running in the background." << std::endl;
}

/**
 * @brief The main entry point for the LSP Docker Container application.
 *
 * This function initializes the application by handling command-line arguments and then proceeds
 * to perform several conditional checks and operations based on those arguments and the system state.
 * It manages device enrollment status and controls cellular modem settings (if applicable). It also
 * handles EdgeIQ call home functionality and waits for device enrollment under certain configurations.
 *
 * @param argc The number of command-line arguments.
 * @param argv An array of character pointers listing all the arguments.
 * @return Returns 0 on successful execution, 1 if execution stops early based on arguments or conditions,
 *         and -1 if an error occurs.
 *
 */
int main(int argc, char *argv[])
{
    try
    {

        if (handleArguments(argc, argv) == 1)
            return 1; // Nothing more to do

        LOG4CXX_INFO(LSPAppLogger, "LSP Docker Container started");
        LOG4CXX_INFO(LSPAppLogger, "Version: " << LSPApp::ReadStringFromFile("/opt/LSP/conf/LSPVersion.txt"));

        StartLSPContainerTimer();

        LSPRun();

        LOG4CXX_INFO(LSPAppLogger, "LSP Application exiting");

        return 0; // Success
    }
    catch (const std::exception &exc)
    {
        // catch anything thrown within try block that derives from std::exception
        LOG4CXX_FATAL(LSPAppLogger, exc.what());

        return -1; // Failure
    }
}
