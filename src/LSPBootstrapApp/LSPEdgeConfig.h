/*
 *   Copyright (c) <2024> <Opengear Inc.>
 *
 *   Lighthouse Service Portal NGCS C++ Lighthouse Enrollment Application
 *
 *   Date: February 14th 2024
 *
 *   Author: <PERSON>
 *
 *   email: <EMAIL>
 */

/**
 * \file
 * Lighthouse Service Portal NGCS Application
 */

#ifndef LSPEdgeConfig_HPP // include guard
#define LSPEdgeConfig_HPP

// Default EdgeIQ account name
#define LSP_EDGE_ACCOUNT_NAME "lsppoc"

// Default EdgeIQ domain - the LSP sub-account name is prepended when we create a CSR
#define LSP_EDGE_ROOT_DOMAIN "opengear.com"

// EdgeIQ Agent application
#define EDGE_IQ_ROOT_DIR "/opt/edge"
#define EDGE_IQ_AGENT_APP EDGE_IQ_ROOT_DIR "/edge"

// This is the file used by the EdgeIQ Agent
#define EDGE_IQ_CONF_DIR EDGE_IQ_ROOT_DIR "/conf"
#define EDGE_IQ_CONF_FILE EDGE_IQ_CONF_DIR "/conf.json"
#define EDGE_IQ_PROD_CONF_FILE EDGE_IQ_CONF_DIR "/conf.json-prod"
#define EDGE_IQ_STAGING_CONF_FILE EDGE_IQ_CONF_DIR "/conf.json-staging"

// EdgeIQ Call Home Endpoint1c
#define POST_CALL_HOME_URL "http://localhost:8084"

// EdgeIQ LSP Status Endpoint
#define POST_STATUS_URL "http://localhost:8084"

#endif // LSPEdgeConfig_HPP
