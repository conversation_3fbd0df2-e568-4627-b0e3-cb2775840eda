import requests
import json
import sys
import argparse

def parse_arguments():
    parser = argparse.ArgumentParser(
        prog='provisionDevice.py',
        description='Python script to provision device and save record to EdgeIQ',
        epilog='LSP Certificate Generation')

    parser.add_argument("--device-url", help="Device under test address", default="", required=False)
    parser.add_argument("--aws-url", help="Not used but parse it", default="", required=False)
    # device-url and aws-url are not used, but added as we are supporting optional serial number as positional argument
    # to maintain backward compatibility with original script
    parser.add_argument("serial_number", nargs='?', default="")
    parser.add_argument("--serial-number-option", help="Serial Number of device", default="", required=False)
    parser.add_argument("--edgeiq-url", help="The certificate authorities ACM PCA ARN", default="",
                        required=False)
    #parser.add_argument("--ats", action="store_true", default=False, help="Executed by ATS")
    args, argv = parser.parse_known_args()
    if not args.serial_number:
        args.serial_number = args.serial_number_option
    return args


class EdgeIqCreds(object):
    def __init__(self, user, pwd):
        self.user = user
        self.pwd = pwd
    user = ""
    pwd = ""


def obtain_credentials():
    """
    If using for development purposes, use either AWS environment variables or
    ~/.aws/credentials file in your user account. Refer AWS access portal for info.
    """
    from autotest.runner.credentials.ats_credentials import ATSCredentials
    title = 'edgeiq'
    cred_store = ATSCredentials()
    creds = cred_store.server_credential(title)
    aws_creds = EdgeIqCreds(creds.name, creds.pwd)
    return aws_creds


# Define EdgeIQ API Endpoints
base_uri = "https://api.stage.edgeiq.io/api/v1"

args = parse_arguments()
if args.edgeiq_url:
    base_uri = args.edgeiq_url

auth_api = f"{base_uri}/platform/user/authenticate"
devices_api = f"{base_uri}/platform/devices"
device_id_api = f"{base_uri}/platform/devices/unique_id/"
device_profiles_api = f"{base_uri}/platform/device_types"

# Get the device serial number
if args.serial_number:
    device_serial_number = args.serial_number
else:
    device_serial_number = sys.argv[1]

# Authentication data
# auth_data = {
#     "email": "<EMAIL>",
#     "password": "VqUA3$-1enEJVxbH"
# }
auth_data = {
    "email": "<EMAIL>",
    "password": "3c3d3e33D)N#"
}

if hasattr(args, 'ats'):
    edgeiq_creds = obtain_credentials()
    if edgeiq_creds:
        auth_data = {
            "email": edgeiq_creds.user,
            "password": edgeiq_creds.pwd
        }

# Authenticate and get a session token
response = requests.post(auth_api, headers={'Content-Type': 'application/json'}, json=auth_data)
response_data = response.json()

# Check for session token
session_token = response_data.get('session_token')
if not session_token:
    print("ERROR: Failed to obtain session token.")
    sys.exit(1)

print(f"Authenticated to EdgeIQ platform: Session token obtained: {session_token}")

headers = {
    'Authorization': session_token,
    'Accept': 'application/json',  # Explicitly accept JSON
    'Content-Type': 'application/json'  # Ensure content-type is also set if you're sending JSON data
}


# First check if the device already exists and return success if it does
lookup_device_api = f"{device_id_api}{device_serial_number}"

response = requests.get(lookup_device_api, headers=headers)
if response.status_code == 200:
    print("INFO: Device already provisioned.")
    sys.exit(0)

response = requests.get(device_profiles_api, headers=headers)
if response.status_code != 200:
    print(f"ERROR: Error fetching device profile data: {response.status_code}")
    print(f"ERROR: {response.text}")
    sys.exit(1)

profiles = response.json()  # This will parse the JSON response body into a Python dictionary

# Extract ID for "NGCS Family Profile"
profile_id = None
for profile in profiles:
    if profile['name'] == "NGCS Family Profile - Stage":
        profile_id = profile['_id']
        break

if not profile_id:
    print("ERROR: Device Profile 'NGCS Family Profile' not found or ID is blank.")
    sys.exit(1)

print(f"INFO: Found Device Profile named 'NGCS Family Profile': {profile_id}")

# Define the JSON for the new device
device_data = {
    "name": device_serial_number,
    "unique_id": device_serial_number,
    "serial": device_serial_number,
    "device_type_id": profile_id,
    "auth_method": "certificate-auth",
    "heartbeat_period": 300,
    "heartbeat_values": ["cpu_usage", "disk_usage", "disk_size"],
    "log_config": {
        "local_level": "trace",
        "forward_level": "critical",
        "forward_frequency_limit": 60
    }
}

print(f"INFO: Adding Device with serial number '{device_serial_number}' to EdgeIQ platform: {base_uri}")

# Add the device to the EdgeIQ Platform
response = requests.post(devices_api, headers=headers, json=device_data)

if response.status_code == 201:
    print("INFO: Device added successfully.")
    sys.exit(0)
else:
    print(f"ERROR: Failed to add device. HTTP response code: {response.status_code}")
    print(response.text)
    sys.exit(1)
