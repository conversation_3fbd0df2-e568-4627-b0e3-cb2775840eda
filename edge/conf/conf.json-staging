{"edge": {"log_level": "trace", "bypass_and_relay": false, "relay_frequency_limit": 10, "ui_port": 9001, "api_port": 9000}, "mqtt": {"log_level": "trace", "broker": {"protocol": "ssl", "host": "mqtt.edgeiq.io", "port": "443", "password": "Dmn2LKZNcYSBd1PAbRMcmEKBG8EDpRjxc0BB5A==", "escrow_token_path": "/opt/escrow_token"}, "client": {"certificate_file_path": "/opt/LSP/pki/opengear.devices.ai.crt", "private_key_file_path": "handle:0x81000000", "tpm_device_path": "/dev/tpmrm0"}}, "platform": {"url": "https://api.edgeiq.io/api/v1/platform"}, "aws": {"greengrass": {"heartbeat_port": 9002}}}