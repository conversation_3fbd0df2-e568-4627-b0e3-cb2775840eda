import requests
import sys
import argparse


def parse_arguments():
    """Parse and return command-line arguments."""
    parser = argparse.ArgumentParser(
        prog='provisionDevice.py',
        description='Python script to provision LSP device record in EdgeIQ',
        epilog='LSP Certificate Generation')

    parser.add_argument(
        "--device-url", help="Device under test address", default="", required=False)
    parser.add_argument(
        "--aws-url", help="Not used but parse it", default="", required=False)
    parser.add_argument("--csr-url", default="", help="CSR File Ignored")
    parser.add_argument("serial_number", nargs='?', default="")
    parser.add_argument("--serial-number-option",
                        help="Serial Number of device", default="", required=False)
    parser.add_argument(
        "--edgeiq-url", help="The certificate authorities ACM PCA ARN", default="", required=False)
    parser.add_argument("--ats", action="store_true",
                        default=False, help="Executed by ATS")

    args, _ = parser.parse_known_args()
    # Set serial_number from serial-number-option if serial_number is not provided
    args.serial_number = args.serial_number or args.serial_number_option
    return args


class EdgeIqCreds:
    """Represents credentials for EdgeIQ platform."""

    def __init__(self, user, pwd):
        self.user = user
        self.pwd = pwd


def obtain_credentials():
    """Obtain credentials for authentication."""
    from autotest.runner.credentials.ats_credentials import ATSCredentials
    cred_store = ATSCredentials()
    creds = cred_store.server_credential('edgeiq')
    return EdgeIqCreds(creds.name, creds.pwd)


def authenticate_to_edgeIQ(auth_api):
    """Authenticate to EdgeIQ and retrieve session token."""
    # Authentication data
    auth_data = {
        "email": "<EMAIL>",  # Replace with your actual email
        "password": "3c3d3e33D)N#"       # Replace with your actual password
    }

    try:
        # Send POST request to authenticate
        response = requests.post(
            auth_api,
            headers={'accept': 'application/json',
                     'content-type': 'application/json'},
            json=auth_data
        )

        # Check if the request was successful (status code 2xx)
        response.raise_for_status()  # Raise an exception for 4xx/5xx errors
        response_data = response.json()
        session_token = response_data.get('session_token', None)

        if session_token:
            print(f"Session token obtained: {session_token}")
        else:
            print("Session token not found in the response data.")
            session_token = None
    except requests.exceptions.RequestException as e:
        print(f"Error during authentication request: {e}")
        session_token = None
    except ValueError:
        print("Error: Response content is not valid JSON.")
        session_token = None

    return session_token


def main():
    """Main execution function."""
    # Parse arguments
    args = parse_arguments()

    # Define EdgeIQ API Endpoints
    base_uri = args.edgeiq_url if args.edgeiq_url else "https://api.edgeiq.io/api/v1"
    auth_api = f"{base_uri}/platform/user/authenticate"
    devices_api = f"{base_uri}/platform/devices"
    # trailing slash is required
    device_id_api = f"{base_uri}/platform/devices/unique_id/"
    device_profiles_api = f"{base_uri}/platform/device_types"

    # Get the device serial number
    device_serial_number = args.serial_number or sys.argv[1]

    # Authenticate to EdgeIQ
    session_token = authenticate_to_edgeIQ(auth_api)
    if not session_token:
        sys.exit(-1)

    # Set headers for subsequent requests
    headers = {
        'Authorization': session_token,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    # Check if the device already exists
    lookup_device_api = f"{device_id_api}{device_serial_number}"
    response = requests.get(lookup_device_api, headers=headers)
    if response.status_code == 200:
        print("INFO: Device already provisioned.")
        sys.exit(0)

    # Fetch device profiles
    response = requests.get(device_profiles_api, headers=headers)
    if response.status_code != 200:
        print(
            f"ERROR: Error fetching device profile data: {response.status_code}")
        sys.exit(1)

    profiles = response.json()

    # Extract ID for "NGCS Family Profile"
    profile_id = next(
        (profile['_id'] for profile in profiles if profile['name'] == "NGCS Family Profile"), None)
    if not profile_id:
        print("ERROR: Device Profile 'NGCS Family Profile' not found.")
        sys.exit(1)

    print(f"INFO: Found Device Profile: {profile_id}")

    # Define device data for provisioning
    device_data = {
        "name": device_serial_number,
        "unique_id": device_serial_number,
        "serial": device_serial_number,
        "device_type_id": profile_id,
        "auth_method": "certificate-auth",
        "heartbeat_period": 300,
        "heartbeat_values": ["cpu_usage", "disk_usage", "disk_size"],
        "log_config": {
            "local_level": "trace",
            "forward_level": "critical",
            "forward_frequency_limit": 60
        }
    }

    # Add the device to the EdgeIQ platform
    response = requests.post(devices_api, headers=headers, json=device_data)
    if response.status_code == 201:
        print("INFO: Device added successfully.")
    else:
        print(
            f"ERROR: Failed to add device. HTTP response code: {response.status_code}")
        sys.exit(1)


if __name__ == "__main__":
    main()
