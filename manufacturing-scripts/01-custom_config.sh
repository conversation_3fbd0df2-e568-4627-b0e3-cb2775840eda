#!/bin/bash

# This script is run on an NGCS device. It creates a TPM-resident private key and then
# uses that private key to create a CSR which is stored in /tmp/LSP/<serialNumber>.csr

# Cause the script to exit immediatly if an error occurs in a command or compound command
set -e

# Initialize OpenSSL config file. Default to production config 
OPENSSL_CONFIG="LSPDeviceCert.conf"

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --csr-url)
            OPENSSL_CONFIG="$2"
            shift # shift to move to the next argument value
            ;;
        *)
            echo "WARNING: Ignoring parameter passed: $1"
            ;;
    esac
    shift # shift to move to the next argument
done

# Check if OPENSSL_CONFIG is set
if [ -z "$OPENSSL_CONFIG" ]; then
    echo "ERROR: --csr-url argument is missing"
    exit 1
fi

# Check if the OpenSSL Config file exists
if [ ! -f "$OPENSSL_CONFIG" ]; then
    echo "ERROR: The file '$OPENSSL_CONFIG' does not exist."
    exit -1
fi

# Check whether the TPM chip supports sha 256 or sha 384
bash ./check-tpm-hw.sh
if [ $? -ne 0 ]; then
    echo "TPM Chip does not support sha 256 or sha 384"
    exit -1
fi

# Define the Attenstation key handle
AttestationKeyHandle=0x81000000

# Define the NVRAM area for certificate storage
LSP_CERT_NVRAM_ADDR="0x01800000"

# Remove any existing handles and objects from the TPM (if they exist)
tpm2_evictcontrol -C o -c $AttestationKeyHandle > /dev/null || true

if tpm2_readpublic -c "$LSP_CERT_NVRAM_ADDR" &>/dev/null; then
     echo "Removing LSP Certificate from TPM"
     tpm2_nvundefine -C o $LSP_CERT_NVRAM_ADDR >/dev/null 2>&1
fi


echo "INFO: Creating TPM-resident Private Key"

CONF_DIR_PATH=".conf"

mkdir -p $CONF_DIR_PATH
tpm2_createprimary -C o -c .conf/rsapss-primary.ctx
tpm2_create -G rsa2048:rsapss:null -u .conf/rsapss-key.pub -r .conf/rsapss-key.priv -C .conf/rsapss-primary.ctx
tpm2_load -C .conf/rsapss-primary.ctx -u .conf/rsapss-key.pub  -r .conf/rsapss-key.priv -c .conf/rsapss-key.ctx

# Make the Endorsement Key Persist across reboots
tpm2_evictcontrol -C o -c .conf/rsapss-key.ctx $AttestationKeyHandle

# Delete the config files
rm -rf $CONF_DIR_PATH

# Get the device serial number from the file system
source ./get-serial-number.sh

# Make the serial number available to the LSPDeviceCert.conf file via an ENV var 
export SerialNumber

# Set the temporary LSP working directory and CSR file path
LSP_TMP_Path="/tmp/LSP"

# Create the temporary LSP directory
mkdir -p $LSP_TMP_Path

# Define the path for the CSR file
CSRFilePath=$LSP_TMP_Path/$SerialNumber.csr

echo "INFO: Using TPM-resident private key to generate CSR for Device Serial Number: $SerialNumber";

# Create CSR using TPM-resident private key
openssl req -provider tpm2 -provider default -propquery '?provider=tpm2' \
	    -new \
            -key handle:$AttestationKeyHandle \
            -config $OPENSSL_CONFIG \
            -reqexts v3_req \
            -out $CSRFilePath

# Make sure the CSR file exists                                                                                                                                        
if [ ! -f "$CSRFilePath" ]; then
    echo "ERROR: The device certificate file $LSP_TMP_Path/$SerialNumber.csr does not exist"
    exit -1;
fi

# Verify the CSR file contents
openssl req -text -noout -verify -in $CSRFilePath

echo "INFO: CSR for device Serial Number: $SerialNumber was created";

exit 0
