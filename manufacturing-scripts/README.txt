The scripts in the manufacturing directory are designed to be run in sequence. 
 
01-custom-config.sh is run on the node and it creates the TPM-resident private key and a CSR
02-test_station_config.sh is run on ATS or it can be run on a Linux host. This script pulls the CSR from the node
and connects to the Opengear AWS private CA (either staging or production) to issue a cert. Next, the script copies
the cert to the node. You will need valid AWS credentials in order to run this and those credentials will need to be
bound to the correct IAM profile.
You can set the AWS credentials as Linux shell environment vars and then run the 02-test_station_config.sh script.
03-custom-config.sh is run on the node and it copies the cert to the TPM. 
04--test_station_config.sh is run on ATS or Linux and it connects to EdgeIQ to provision the device there using the
serial number as the key. You will need valid EdgeIQ credentials in either staging or production. Anyone with EdgeIQ admin
credentials can create an EdgeIQ account if you don't have one.
