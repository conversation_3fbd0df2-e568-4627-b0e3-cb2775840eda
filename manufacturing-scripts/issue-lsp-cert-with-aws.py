# Script that reads the LSP device Certificate Signing Request from /tmp/LSP/<serialNumber.csr>
# and issues the device certificate using the AWS Private Certificate Authority.
# Internet Connectivity is required.

# Arguments:
#     Serial Number      - device serial number
#     LSP Temp Dir Path  - temp directory where the CSR and Certificate are stored

import os
import time
import sys
import subprocess
import argparse

# Import AWS Python SDK
import boto3
from botocore.exceptions import ClientError


def parse_arguments():
    parser = argparse.ArgumentParser(
        prog='issue-lsp-cert-with-aws.py',
        description='Python script using aws sdk to obtain certificate from CA',
        epilog='LSP Certificate Generation')
    parser.add_argument("serial_number", nargs='?', default="")
    parser.add_argument("--serial-number-option", help="Serial Number of device", default="", required=False)
    parser.add_argument("--tmp-dir", help="Location where the certificate will be placed. "
                                          "Directory should exist.")
    parser.add_argument("--aws-url", help="The certificate authorities ACM PCA ARN", required=False)
    parser.add_argument("--ats", action="store_true", default=False, help="Executed by ATS")
    args, argv = parser.parse_known_args()
    if not args.serial_number:
        args.serial_number = args.serial_number_option
    return args


class AwsCreds(object):
    def __init__(self, region, id, secret, token):
        self.region = region
        self.id = id
        self.secret = secret
        self.token = token
    region = ""
    id = ""
    secret = ""
    token = ""

    def all(self):
        return (self.region, self.id, self.secret, self.token)


def obtain_credentials():
    """
    If using for development purposes, use either AWS environment variables or
    ~/.aws/credentials file in your user account. Refer AWS access portal for info.
    """
    from autotest.runner.credentials.ats_credentials import ATSCredentials
    title = 'aws'
    cred_store = ATSCredentials()
    creds = cred_store.server_credential(title)
    aws_creds = AwsCreds(creds.name, creds.pwd, creds.api_key, creds.token)
    return aws_creds


def main():
    args = parse_arguments()

    serial_number = args.serial_number
    tmp_dir = args.tmp_dir
    csr_file_path = f"{tmp_dir}/{serial_number}.csr"
    crt_file_path = f"{tmp_dir}/{serial_number}.crt"

    # Set up AWS credentials directly
    region_name = 'us-east-1'
    aws_session_token = ""

    if args.ats:
        aws_creds = obtain_credentials()
        region_name, aws_access_key_id, aws_secret_access_key, aws_session_token = aws_creds.all()

        # Initialize a session using your credentials
        session = boto3.Session(
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            aws_session_token=aws_session_token,
            region_name=region_name
        )

    else:
        #region_name='ap-southeast-2'

        # Initialize a session using your credentials
        session = boto3.Session(
#        aws_access_key_id=aws_access_key_id,
#        aws_secret_access_key=aws_secret_access_key,
            region_name=region_name
         )

    # Create an ACM-PCA client from your session
    acm_pca_client = session.client('acm-pca')

    # Replace 'your-certificate-authority-arn' with your actual ACM PCA ARN
    #certificate_authority_arn = "arn:aws:acm-pca:us-east-1:895234880665:certificate-authority/cc9496dc-ce50-4221-a27b-a7808a533bf6"
    #certificate_authority_arn = "arn:aws:acm-pca:us-east-1:767398044742:certificate-authority/10f06dc8-4797-4539-b3eb-1be9d2231cb9"
    #certificate_authority_arn = "arn:aws:acm-pca:us-east-1:767398044742:certificate-authority/5764fe04-1f0e-4ea7-8b57-b585e731088c"
    
    if args.aws_url:
        certificate_authority_arn = args.aws_url

    print (f"INFO: Issuing certificate using ARN $certificate_authority_arn")
        
    print (f"INFO: Issuing certificate using CSR {csr_file_path}")

    # Issue certificate using the CSR
    try:
        response = acm_pca_client.issue_certificate(
            CertificateAuthorityArn=certificate_authority_arn,
            Csr=open(csr_file_path, 'rb').read(),
            SigningAlgorithm='SHA256WITHRSA',
            Validity={'Value': 18, 'Type': 'MONTHS'}
        )
        certificate_arn = response['CertificateArn']

    except FileNotFoundError:
        print(f"ERROR: The CSR file {csr_file_path} could not be found.")
        sys.exit(1)
    except PermissionError:
        print(f"ERROR: You do not have permission to read the CSR file {csr_file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"ERROR: Error opening CSR file {csr_file_path}: {e}")
        sys.exit(1)

#   print(f"Issued Certificate ARN: {certificate_arn}")

    # Retrieve the issued certificate
    max_retries = 5
    retry_count = 0

    print(f"INFO: Certificate Issued. Retriving certificate from AWS")
    
    while retry_count < max_retries:
        try:
            cert_response = acm_pca_client.get_certificate(
                CertificateAuthorityArn=certificate_authority_arn,
                CertificateArn=certificate_arn
            )
            with open(crt_file_path, 'w') as cert_file:
                cert_file.write(cert_response['Certificate'])
            print(f"Certificate saved to {crt_file_path}")
            break  # Exit loop on success

        except ClientError as e:
            if e.response['Error']['Code'] == 'RequestInProgressException':
                print("Request in progress, retrying...")
                retry_count += 1
                time.sleep(1)  # Wait for 1 second before retrying
            else:
                print(f"ERROR: Error retrieving certificate: {e}")
                sys.exit(1)

        except Exception as e:
            print(f"ERROR: Error retrieving certificate: {e}")
            sys.exit(1)

        if retry_count == max_retries:
            print("ERROR: Maximum retries reached, certificate retrieval failed.")
            sys.exit(1)

            
if __name__ == "__main__":
    main()
